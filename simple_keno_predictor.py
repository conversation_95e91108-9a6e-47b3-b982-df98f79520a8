#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import random
import mysql.connector
from collections import Counter
from itertools import combinations
import datetime

def connect_db():
    """Kết nối database"""
    return mysql.connector.connect(
        host='localhost',
        user='root',
        password='',
        database='keno_prediction'
    )

class SimpleKenoPredictor:
    def __init__(self):
        # Money Management Settings
        self.daily_capital = 3_000_000  # 3M VNĐ/ngày
        self.target_profit = 2_000_000  # 2M VNĐ/ngày
        self.stop_loss_ratio = 0.3  # Dừng khi còn 30% vốn
        
        # Ticket Settings
        self.base_ticket_price = 10_000  # 10k VNĐ/vé
        self.min_ticket_price = 10_000
        self.max_ticket_price = 100_000
        self.win_multiplier = 3.2  # Tỷ lệ thắng
        
        # Betting Settings
        self.base_tickets = 10  # Số vé cơ bản
        self.min_tickets = 5
        self.max_tickets = 20
        
        # <PERSON><PERSON>e Settings
        self.martingale_periods_remaining = 0
        self.martingale_type = ""
        
        # Skip Settings
        self.skip_periods_remaining = 0
        self.recent_results = []
        
        # Phase 2: Adaptive Learning Settings
        self.method_performance = {
            'lstm': [],
            'excluded': [],
            'pattern': [],
            'hot': [],
            'cold': []
        }
        self.method_weights = {
            'lstm': 0.30,
            'excluded': 0.25,
            'pattern': 0.20,
            'hot': 0.15,
            'cold': 0.10
        }
        self.performance_window = 20
        self.confidence_scores = []
        
        print("✅ Simple Keno Predictor initialized")
        print(f"💰 Vốn/ngày: {self.daily_capital:,} VNĐ")
        print(f"🎯 Mục tiêu: {self.target_profit:,} VNĐ/ngày")
        print(f"🎫 Giá vé: {self.min_ticket_price:,} - {self.max_ticket_price:,} VNĐ")
        print(f"🏆 Tỷ lệ thắng: {self.win_multiplier}x")
        print(f"📊 Số vé cơ bản: {self.base_tickets} vé/lần")

    def get_day_draws(self, date):
        """Lấy tất cả kì của một ngày"""
        try:
            print(f"🔍 Trying to load data for date: {date}")
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            # Debug: Kiểm tra dữ liệu có sẵn
            cursor.execute("SELECT MIN(date), MAX(date), COUNT(*) FROM histories_keno")
            result = cursor.fetchone()
            min_date, max_date, total = result['MIN(date)'], result['MAX(date)'], result['COUNT(*)']
            print(f"📊 Database range: {min_date} to {max_date} ({total} records)")

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (date,))
            rows = cursor.fetchall()
            print(f"📅 Found {len(rows)} records for {date}")

            cursor.close()
            conn.close()

            if not rows:
                print(f"⚠️ No data found for {date}, using sample data")
                return self._generate_sample_data()

            # Chuyển đổi results từ string thành list
            for row in rows:
                if isinstance(row['results'], str):
                    row['results'] = [int(n.strip()) for n in row['results'].split(',') if n.strip()]
                elif isinstance(row['results'], list):
                    pass
                else:
                    print(f"⚠️ Unexpected results format: {type(row['results'])}")
                    row['results'] = []

            print(f"✅ Successfully loaded {len(rows)} periods for {date}")
            return rows

        except Exception as e:
            print(f"❌ Error loading day draws: {e}")
            print(f"🔄 Using sample data instead")
            return self._generate_sample_data()

    def _generate_sample_data(self):
        """Tạo dữ liệu mẫu để demo"""
        sample_data = []
        for i in range(80):  # 80 kì
            # Tạo 20 số ngẫu nhiên từ 1-80
            results = sorted(random.sample(range(1, 81), 20))
            sample_data.append({
                'time': f"08:{i:02d}:00",
                'results': results,
                'period': i + 1
            })
        return sample_data

    def calculate_dynamic_ticket_price(self, consecutive_losses, consecutive_wins, cumulative_profit, recent_big_win=False):
        """Tính giá vé động dựa trên tình hình thắng/thua"""
        base_price = self.base_ticket_price
        price = base_price

        # Martingale cải tiến - tăng giá vé khi thua liên tiếp
        if consecutive_losses >= 5:
            price = base_price * 2.0
            if self.martingale_periods_remaining <= 0:
                self.martingale_periods_remaining = 3
                self.martingale_type = "100%"
        elif consecutive_losses >= 3:
            price = base_price * 1.5
            if self.martingale_periods_remaining <= 0:
                self.martingale_periods_remaining = 2
                self.martingale_type = "50%"

        # Tăng giá vé khi thắng liên tiếp (aggressive betting)
        elif consecutive_wins >= 6:
            price = base_price * 3.0
        elif consecutive_wins >= 3:
            price = base_price * 2.0
        elif recent_big_win:
            price = base_price * 1.4

        # Tăng cược dựa trên lợi nhuận tích lũy khi thắng liên tiếp
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            profit_boost = cumulative_profit * 0.5
            profit_multiplier = min(profit_boost / base_price, 2.0)
            price = base_price * (1 + profit_multiplier)

        else:
            price = base_price

        # Cho phép vượt max_ticket_price trong trường hợp martingale, aggressive betting
        max_allowed_price = self.max_ticket_price * 3 if (consecutive_losses >= 3 or consecutive_wins >= 2) else self.max_ticket_price
        return max(self.min_ticket_price, min(price, max_allowed_price))

    def calculate_bet_size(self, current_capital, consecutive_losses, win_rate, ticket_price, consecutive_wins=0, cumulative_profit=0):
        """Tính số vé cần mua"""
        base_tickets = self.base_tickets

        # Điều chỉnh dựa trên win rate
        if win_rate >= 0.40:
            base_tickets = self.base_tickets
        elif win_rate >= 0.35:
            base_tickets = max(self.base_tickets - 2, self.min_tickets)
        elif win_rate >= 0.30:
            base_tickets = max(self.base_tickets - 3, self.min_tickets)
        else:
            base_tickets = self.min_tickets

        # Martingale - tăng số vé khi thua liên tiếp
        if consecutive_losses >= 5:
            base_tickets = int(base_tickets * 2.0)
        elif consecutive_losses >= 3:
            base_tickets = int(base_tickets * 1.5)

        # Tăng cược khi thắng liên tiếp
        elif consecutive_wins >= 6:
            base_tickets = int(base_tickets * 3.0)
        elif consecutive_wins >= 3:
            base_tickets = int(base_tickets * 2.0)

        # Tăng số vé dựa trên lợi nhuận khi thắng liên tiếp
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            profit_boost = cumulative_profit * 0.5
            additional_tickets = int(profit_boost // ticket_price)
            max_additional = int(base_tickets * 0.5)
            additional_tickets = min(additional_tickets, max_additional)
            if additional_tickets > 0:
                base_tickets += additional_tickets

        # Giới hạn theo vốn còn lại
        max_affordable = current_capital // ticket_price
        final_tickets = min(base_tickets, max_affordable, self.max_tickets * 2)

        return max(final_tickets, self.min_tickets) if final_tickets >= self.min_tickets else 0

    def should_skip_prediction(self, recent_win_rate, consecutive_losses):
        """Kiểm tra có nên skip dự đoán không dựa trên tỷ lệ trượt cao"""
        # Nếu đang trong thời gian skip
        if self.skip_periods_remaining > 0:
            self.skip_periods_remaining -= 1
            print(f"      ⏸️ SKIP: Còn {self.skip_periods_remaining} kì cần skip")
            return True

        # Kiểm tra tỷ lệ trượt trong 3-5 kỳ gần nhất
        if len(self.recent_results) >= 3:
            recent_periods = min(5, len(self.recent_results))
            recent_data = self.recent_results[-recent_periods:]

            total_miss_rate = 0
            valid_periods = 0

            for result in recent_data:
                if 'miss_rate' in result and result['miss_rate'] is not None:
                    total_miss_rate += result['miss_rate']
                    valid_periods += 1

            if valid_periods >= 3:
                avg_miss_rate = total_miss_rate / valid_periods

                # Nếu tỷ lệ trượt >= 80% thì skip 1 kỳ
                if avg_miss_rate >= 0.80:
                    skip_periods = 1
                    self.skip_periods_remaining = skip_periods - 1
                    print(f"      ⏸️ SKIP STRATEGY: Tỷ lệ trượt cao {avg_miss_rate:.1%} trong {valid_periods} kỳ gần nhất → Skip {skip_periods} kỳ")

                    # Decay miss rate để tránh skip vô hạn
                    for result in recent_data:
                        if 'miss_rate' in result:
                            result['miss_rate'] *= 0.8

                    return True

        return False

    def update_martingale_tracking(self):
        """Cập nhật tracking cho Martingale"""
        if self.martingale_periods_remaining > 0:
            self.martingale_periods_remaining -= 1
            if self.martingale_periods_remaining <= 0:
                print(f"      ✅ MARTINGALE {self.martingale_type} kết thúc")
                self.martingale_type = ""

    def calculate_profit(self, tickets_bought, winning_tickets, ticket_price):
        """Tính lợi nhuận từ một lần chơi"""
        cost = tickets_bought * ticket_price
        revenue = winning_tickets * ticket_price * self.win_multiplier
        profit = revenue - cost
        return profit, cost, revenue

    def get_lstm_predictions(self, day_results, num_predictions=10):
        """Dự đoán LSTM các số có khả năng TRƯỢT cao - dựa trên frequency thấp"""
        try:
            all_numbers = []
            for result in day_results:
                all_numbers.extend(result['results'])

            if not all_numbers:
                return list(range(1, num_predictions + 1))

            if len(day_results) < num_predictions:
                return list(range(1, num_predictions + 1))

            # Đếm tần suất và lấy số có tần suất THẤP NHẤT (khả năng trượt cao)
            frequency = Counter(all_numbers)
            all_numbers_set = set(range(1, 81))
            number_frequencies = {num: frequency.get(num, 0) for num in all_numbers_set}

            # Sắp xếp theo tần suất TĂNG DẦN (số ít xuất hiện nhất trước)
            least_common = sorted(number_frequencies.items(), key=lambda x: x[1])
            predictions = [num for num, _ in least_common[:num_predictions]]

            print(f"      📊 LSTM top {len(predictions)} số TRƯỢT (từ {len(day_results)} kì trong ngày): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ LSTM error: {e}")
            return list(range(1, num_predictions + 1))

    def get_excluded_numbers(self, day_results):
        """Lấy các số bị loại trừ - sử dụng logic gap analysis"""
        try:
            if not day_results:
                return set()

            recent_results = day_results[-20:]
            all_recent_numbers = []
            for result in recent_results:
                all_recent_numbers.extend(result['results'])

            frequency = Counter(all_recent_numbers)
            all_numbers = set(range(1, 81))

            freq_values = list(frequency.values())
            if freq_values:
                threshold = sorted(freq_values)[len(freq_values) // 4] if len(freq_values) > 4 else 1
                excluded = {num for num in all_numbers if frequency.get(num, 0) <= threshold}
            else:
                excluded = set(range(1, 21))

            excluded_list = sorted(list(excluded))
            print(f"      📊 Excluded ({len(excluded_list)}): {excluded_list[:5]}... (gap analysis)")
            return excluded
        except Exception as e:
            print(f"      ❌ Excluded error: {e}")
            return set(range(21, 41))

    def get_hot_numbers(self, day_results, num_predictions=10, lookback=30):
        """Lấy các số nóng - xuất hiện nhiều trong thời gian gần đây"""
        try:
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if not recent_results:
                return list(range(1, num_predictions + 1))

            hot_numbers = []
            for result in recent_results:
                hot_numbers.extend(result['results'])

            frequency = Counter(hot_numbers)
            most_common = frequency.most_common(num_predictions)
            predictions = [num for num, _ in most_common]

            print(f"      🔥 Hot Numbers top {len(predictions)} (từ {len(recent_results)} kì gần nhất): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ Hot Numbers error: {e}")
            return list(range(1, num_predictions + 1))

    def get_cold_numbers(self, day_results, num_predictions=10, lookback=50):
        """Lấy các số lạnh - ít xuất hiện trong thời gian gần đây"""
        try:
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if not recent_results:
                return list(range(71, 71 + num_predictions))

            cold_numbers = []
            for result in recent_results:
                cold_numbers.extend(result['results'])

            frequency = Counter(cold_numbers)
            all_numbers = set(range(1, 81))
            number_counts = {num: frequency.get(num, 0) for num in all_numbers}

            sorted_cold = sorted(number_counts.items(), key=lambda x: x[1])
            predictions = [num for num, _ in sorted_cold[:num_predictions]]

            print(f"      ❄️ Cold Numbers top {len(predictions)} (từ {len(recent_results)} kì gần nhất): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ Cold Numbers error: {e}")
            return list(range(71, 71 + num_predictions))

    def analyze_recent_patterns(self, day_results, lookback=20):
        """Phân tích patterns gần đây để điều chỉnh strategy"""
        try:
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if len(recent_results) < 5:
                return {}

            number_gaps = {}
            for num in range(1, 81):
                last_seen = -1
                gaps = []
                for i, result in enumerate(recent_results):
                    if num in result['results']:
                        if last_seen >= 0:
                            gaps.append(i - last_seen)
                        last_seen = i

                if gaps and len(gaps) >= 2:
                    avg_gap = sum(gaps) / len(gaps)
                    current_gap = len(recent_results) - last_seen - 1 if last_seen >= 0 else len(recent_results)

                    if avg_gap > 0:
                        gap_score = avg_gap - current_gap
                        number_gaps[num] = gap_score

            return number_gaps
        except Exception as e:
            print(f"      ❌ Pattern analysis error: {e}")
            return {}

    def get_pattern_predictions(self, day_results, num_predictions=10):
        """Dự đoán dựa trên pattern chu kỳ"""
        try:
            patterns = self.analyze_recent_patterns(day_results)

            if not patterns:
                return list(range(31, 31 + num_predictions))

            sorted_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)
            predictions = [num for num, _ in sorted_patterns[:num_predictions]]

            print(f"      🔄 Pattern Predictions top {len(predictions)}: {predictions[:5]}... (cycle analysis)")
            return predictions
        except Exception as e:
            print(f"      ❌ Pattern Predictions error: {e}")
            return list(range(31, 31 + num_predictions))

    def get_ensemble_predictions(self, day_results, actual_results=None):
        """Kết hợp nhiều phương pháp với trọng số adaptive"""
        try:
            print(f"      🎯 ENSEMBLE PREDICTIONS (Simple Version):")

            # 1. Các phương pháp cơ bản
            lstm_pred = self.get_lstm_predictions(day_results, 15)
            excluded_pred = list(self.get_excluded_numbers(day_results))
            hot_numbers = self.get_hot_numbers(day_results, 10)
            cold_numbers = self.get_cold_numbers(day_results, 10)
            pattern_pred = self.get_pattern_predictions(day_results, 15)

            print(f"      📝 PHƯƠNG PHÁP DỰ ĐOÁN:")
            print(f"         🤖 LSTM: Dự đoán số có khả năng TRƯỢT cao (tần suất thấp trong ngày)")
            print(f"         🚫 EXCLUDED: Dự đoán số có khả năng TRƯỢT cao (gap analysis)")
            print(f"         🔥 HOT: Số nóng (xuất hiện nhiều gần đây) - có thể RA")
            print(f"         ❄️ COLD: Số lạnh (ít xuất hiện gần đây) - có thể TRƯỢT")
            print(f"         🔄 PATTERN: Dự đoán theo chu kỳ xuất hiện - có thể RA hoặc TRƯỢT")

            # 2. Tạo predictions dictionary
            predictions_dict = {
                'lstm': lstm_pred,
                'excluded': excluded_pred,
                'pattern': pattern_pred,
                'hot': hot_numbers,
                'cold': cold_numbers
            }

            # 3. Cập nhật performance nếu có actual results
            if actual_results is not None:
                self.update_method_performance(predictions_dict, actual_results)
                self.calculate_adaptive_weights()

            # 4. Tính confidence score
            confidence = self.calculate_confidence_score(predictions_dict)

            # 5. Sử dụng adaptive weights
            weights = self.method_weights
            print(f"      📊 Current weights: {[(k, f'{v:.3f}') for k, v in weights.items()]}")

            # 6. Tính điểm tổng hợp với adaptive weights
            final_scores = {}
            for method, pred_list in predictions_dict.items():
                weight = weights[method]
                for i, num in enumerate(pred_list[:10]):
                    score = (10 - i) * weight
                    final_scores[num] = final_scores.get(num, 0) + score

            # 7. Chọn top 6 số tốt nhất
            sorted_scores = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
            final_6 = [num for num, _ in sorted_scores[:6]]

            print(f"      🎯 Ensemble Final 6: {final_6}")
            print(f"      📊 Top ensemble scores: {[(num, f'{score:.2f}') for num, score in sorted_scores[:6]]}")

            return final_6, confidence, predictions_dict
        except Exception as e:
            print(f"      ❌ Ensemble error: {e}")
            # Fallback
            fallback = list(range(1, 7))
            return fallback, 0.5, {}

    def update_method_performance(self, predictions_dict, actual_results):
        """Cập nhật performance của từng phương pháp"""
        try:
            for method, pred_list in predictions_dict.items():
                if method in self.method_performance:
                    hits = len(set(pred_list[:6]) & set(actual_results))
                    accuracy = hits / 6.0
                    self.method_performance[method].append(accuracy)

                    if len(self.method_performance[method]) > self.performance_window:
                        self.method_performance[method].pop(0)

                    print(f"      📊 {method.upper()} accuracy: {accuracy:.2f} ({hits}/6 hits)")
        except Exception as e:
            print(f"      ❌ Performance update error: {e}")

    def calculate_adaptive_weights(self):
        """Tính trọng số adaptive dựa trên performance"""
        try:
            new_weights = {}
            total_performance = 0

            for method in self.method_weights.keys():
                if self.method_performance[method]:
                    recent_performance = self.method_performance[method][-10:]
                    avg_performance = sum(recent_performance) / len(recent_performance)
                    boosted_performance = avg_performance + 0.1  # Base boost
                    new_weights[method] = boosted_performance
                    total_performance += boosted_performance
                else:
                    new_weights[method] = 0.1
                    total_performance += 0.1

            # Normalize weights
            if total_performance > 0:
                for method in new_weights:
                    old_weight = self.method_weights[method]
                    new_weight = new_weights[method] / total_performance

                    # Smooth transition
                    self.method_weights[method] = old_weight * 0.7 + new_weight * 0.3

                    change = ((new_weight - old_weight) / old_weight) * 100 if old_weight > 0 else 0
                    print(f"      🎯 ADAPTIVE WEIGHTS UPDATE:")
                    print(f"         {method}: {old_weight:.3f} → {new_weight:.3f} ({change:+.1f}%)")

        except Exception as e:
            print(f"      ❌ Adaptive weights error: {e}")

    def calculate_confidence_score(self, predictions_dict):
        """Tính confidence score dựa trên consensus và performance"""
        try:
            # Tính consensus score
            all_predictions = []
            for pred_list in predictions_dict.values():
                all_predictions.extend(pred_list[:6])

            frequency = Counter(all_predictions)
            max_freq = max(frequency.values()) if frequency else 1
            consensus_score = max_freq / len(predictions_dict)

            # Tính performance score
            if any(self.method_performance.values()):
                recent_performances = []
                for method_perf in self.method_performance.values():
                    if method_perf:
                        recent_performances.extend(method_perf[-5:])
                performance_score = sum(recent_performances) / len(recent_performances) if recent_performances else 0.5
            else:
                performance_score = 0.5

            confidence = (consensus_score + performance_score) / 2
            print(f"      🎯 CONFIDENCE SCORE: {confidence:.3f} (consensus: {consensus_score:.3f}, performance: {performance_score:.3f})")

            return confidence
        except Exception as e:
            print(f"      ❌ Confidence calculation error: {e}")
            return 0.5

    def generate_smart_combinations(self, final_6_numbers, num_combos=15):
        """Tạo combinations thông minh"""
        try:
            if len(final_6_numbers) < 4:
                return []

            print(f"      🎲 SMART COMBINATIONS:")
            all_possible_combos = list(combinations(final_6_numbers, 4))

            if len(all_possible_combos) <= num_combos:
                selected_combos = all_possible_combos
                print(f"      ✅ Using all {len(selected_combos)} combinations")
            else:
                selected_combos = random.sample(all_possible_combos, num_combos)
                print(f"      🎲 Using {num_combos} random combinations")

            return selected_combos
        except Exception as e:
            print(f"      ❌ Smart combinations error: {e}")
            return []

    def predict_period(self, day_draws, period_index):
        """Dự đoán cho một kì cụ thể"""
        try:
            if period_index >= len(day_draws):
                return None

            # Lấy dữ liệu training (các kì trước đó)
            training_data = day_draws[:period_index]
            if len(training_data) < 5:
                print(f"      ⚠️ Không đủ dữ liệu training: {len(training_data)} kì")
                return None

            # Lấy kết quả thực tế của kì hiện tại
            current_period = day_draws[period_index]
            actual_results = current_period['results']

            print(f"      🔍 Training data: {len(training_data)} kì, sample: {training_data[-1]['results'][:10]}")

            # Ensemble predictions
            final_6_strategy, confidence, predictions_dict = self.get_ensemble_predictions(training_data, actual_results)

            # Tạo combinations
            selected_combos = self.generate_smart_combinations(final_6_strategy, 15)

            # Tính số vé thắng
            actual_missing = [i for i in range(1, 81) if i not in actual_results]
            winning_combos = 0
            for combo in selected_combos:
                missing_count = sum(1 for num in combo if num in actual_missing)
                if missing_count >= 4:
                    winning_combos += 1

            return {
                'period': current_period['period'],
                'time': current_period['time'],
                'actual_results': actual_results,
                'final_6_strategy': final_6_strategy,
                'selected_combos': selected_combos,
                'winning_combos': winning_combos,
                'total_combos': len(selected_combos),
                'confidence': confidence,
                'predictions_dict': predictions_dict
            }

        except Exception as e:
            print(f"      ❌ Prediction error: {e}")
            return None

    def test_date_range(self, start_date, end_date, min_periods=50):
        """Test predictor trên một khoảng thời gian"""
        print(f"\n🎯 TESTING DATE RANGE: {start_date} → {end_date}")
        print(f"⚙️ Config: Minimum periods = {min_periods}")
        print("="*60)

        # Parse dates
        start_dt = datetime.datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.datetime.strptime(end_date, '%Y-%m-%d')

        total_stats = {
            'total_days': 0,
            'total_predictions': 0,
            'winning_combos': 0,
            'total_combos': 0,
            'total_profit': 0,
            'total_cost': 0,
            'total_revenue': 0,
            'daily_stats': {}
        }

        current_dt = start_dt
        while current_dt <= end_dt:
            test_date = current_dt.strftime('%Y-%m-%d')
            print(f"\n📅 Testing {test_date}...")

            # Load data cho ngày này
            day_draws = self.get_day_draws(test_date)
            if len(day_draws) < min_periods:
                print(f"   ⚠️ Không đủ dữ liệu: {len(day_draws)} < {min_periods} kì")
                current_dt += datetime.timedelta(days=1)
                continue

            # Reset daily variables
            current_capital = self.daily_capital
            daily_profit = 0
            daily_cost = 0
            daily_revenue = 0
            consecutive_losses = 0
            consecutive_wins = 0
            last_period_profit = 0
            day_predictions = 0
            day_winning = 0
            day_total = 0

            # Test từng kì
            for period_index in range(min_periods, len(day_draws)):
                # Tính win rate gần đây
                recent_results = []
                for i in range(max(0, period_index - 10), period_index):
                    if i in getattr(self, '_period_results', {}):
                        recent_results.append(self._period_results[i])

                recent_wins = sum(1 for profit in recent_results if profit > 0)
                recent_win_rate = recent_wins / len(recent_results) if recent_results else 0.3

                # Kiểm tra có nên skip không
                if self.should_skip_prediction(recent_win_rate, consecutive_losses):
                    print(f"   ⏸️ KÌ {period_index + 1} - SKIP (dự đoán khó)")
                    continue

                # Kiểm tra điều kiện dừng
                if daily_profit >= self.target_profit:
                    print(f"   🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                    break

                if current_capital <= self.daily_capital * self.stop_loss_ratio:
                    print(f"   🛑 STOP LOSS! Vốn còn: {current_capital:,} VNĐ - DỪNG CHƠI")
                    break

                result = self.predict_period(day_draws, period_index)
                if result:
                    # Detect big win
                    recent_big_win = last_period_profit > 100_000

                    # Tính giá vé động
                    ticket_price = self.calculate_dynamic_ticket_price(consecutive_losses, consecutive_wins, daily_profit, recent_big_win)

                    # Tính số vé cần mua
                    tickets_to_buy = self.calculate_bet_size(current_capital, consecutive_losses, recent_win_rate, ticket_price, consecutive_wins, daily_profit)

                    if tickets_to_buy == 0:
                        print(f"   💸 Không đủ vốn để mua vé - DỪNG CHƠI")
                        break

                    # Tính số vé thắng
                    tickets_to_display = min(tickets_to_buy, len(result['selected_combos']))
                    winning_tickets = result['winning_combos']

                    # Tính lợi nhuận
                    period_profit, period_cost, period_revenue = self.calculate_profit(tickets_to_display, winning_tickets, ticket_price)

                    # Cập nhật vốn và thống kê
                    current_capital += period_profit
                    daily_profit += period_profit
                    daily_cost += period_cost
                    daily_revenue += period_revenue

                    # Cập nhật consecutive wins/losses
                    last_period_profit = period_profit
                    if period_profit > 0:
                        consecutive_losses = 0
                        consecutive_wins += 1
                    else:
                        consecutive_losses += 1
                        consecutive_wins = 0

                    # Cập nhật tracking
                    self.update_martingale_tracking()
                    if not hasattr(self, '_period_results'):
                        self._period_results = {}
                    self._period_results[period_index] = period_profit

                    # Tính miss rate
                    actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]
                    predicted_numbers = result['final_6_strategy']
                    correct_misses = sum(1 for num in predicted_numbers if num in actual_missing)
                    miss_rate = correct_misses / len(predicted_numbers) if predicted_numbers else 0

                    # Cập nhật recent_results
                    period_result = {
                        'period': period_index + 1,
                        'profit': period_profit,
                        'miss_rate': miss_rate,
                        'predicted_numbers': predicted_numbers,
                        'actual_missing': actual_missing
                    }

                    if not hasattr(self, 'recent_results'):
                        self.recent_results = []
                    self.recent_results.append(period_result)
                    if len(self.recent_results) > 10:
                        self.recent_results.pop(0)

                    day_predictions += 1
                    day_winning += winning_tickets
                    day_total += tickets_to_display
                    total_stats['total_predictions'] += 1
                    total_stats['winning_combos'] += winning_tickets
                    total_stats['total_combos'] += tickets_to_display
                    total_stats['total_profit'] += period_profit
                    total_stats['total_cost'] += period_cost
                    total_stats['total_revenue'] += period_revenue

                    # Print period details
                    profit_icon = "🟢" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"
                    print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")
                    print(f"      🎯 Final 6: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])}")
                    print(f"      💰 Mua {tickets_to_display} vé @ {ticket_price:,} VNĐ/vé → Thắng {winning_tickets} vé → {profit_icon} {period_profit:+,} VNĐ")
                    print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")
                    print(f"      📈 Lợi nhuận tích lũy: {profit_icon} {daily_profit:+,} VNĐ")

            if day_predictions > 0:
                total_stats['total_days'] += 1
                win_rate = (day_winning / day_total) * 100 if day_total > 0 else 0

                total_stats['daily_stats'][test_date] = {
                    'predictions': day_predictions,
                    'total_combos': day_total,
                    'winning_combos': day_winning,
                    'win_rate': win_rate,
                    'daily_profit': daily_profit
                }

                print(f"   📊 Ngày {test_date}: {day_predictions} kì, {day_winning}/{day_total} vé thắng ({win_rate:.1f}%)")

            current_dt += datetime.timedelta(days=1)

        # Hiển thị kết quả tổng hợp
        self._display_final_results(total_stats, start_date, end_date)

    def _display_final_results(self, total_stats, start_date, end_date):
        """Hiển thị kết quả tổng hợp"""
        print(f"\n🎯 THỐNG KÊ TỔNG HỢP ({start_date} → {end_date})")
        print("="*60)
        print(f"Tổng: {total_stats['total_days']} ngày, {total_stats['total_predictions']} predictions")

        if total_stats['daily_stats']:
            print(f"\n📊 THỐNG KÊ TỪNG NGÀY:")
            print("   Ngày        | Kì  | Vé thắng/Tổng vé | Tỷ lệ % | Lợi nhuận")
            print("   " + "-"*62)

            for date, stats in total_stats['daily_stats'].items():
                profit_display = f"{stats['daily_profit']:+,.0f}" if stats['daily_profit'] != 0 else "0"
                print(f"   {date} | {stats['predictions']:2d}  | "
                      f"{stats['winning_combos']:3d}/{stats['total_combos']:3d}        | "
                      f"{stats['win_rate']:5.1f}% | {profit_display:>10}")

        print(f"\n💰 MONEY MANAGEMENT RESULTS:")
        if total_stats['total_cost'] > 0:
            total_roi = (total_stats['total_profit'] / total_stats['total_cost']) * 100
            print(f"   Tổng chi phí: {total_stats['total_cost']:,} VNĐ")
            print(f"   Tổng doanh thu: {total_stats['total_revenue']:,} VNĐ")
            print(f"   Tổng lợi nhuận: {total_stats['total_profit']:+,} VNĐ")
            print(f"   ROI: {total_roi:+.1f}%")
            print(f"   Lợi nhuận/ngày: {total_stats['total_profit']/total_stats['total_days']:+,.0f} VNĐ")

        print(f"\n🎲 Trading Performance:")
        if total_stats['total_combos'] > 0:
            win_rate = (total_stats['winning_combos'] / total_stats['total_combos']) * 100
            print(f"   Tổng vé thắng: {total_stats['winning_combos']}")
            print(f"   Tổng vé đã mua: {total_stats['total_combos']}")
            print(f"   Tỷ lệ thắng vé: {win_rate:.1f}%")
            print(f"   Trung bình vé thắng/kì: {total_stats['winning_combos']/total_stats['total_predictions']:.1f}")

        print("="*60)


def test_range(start_date, end_date, min_periods=50):
    """Function để test từ command line"""
    predictor = SimpleKenoPredictor()
    return predictor.test_date_range(start_date, end_date, min_periods)


if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3 or len(sys.argv) > 4:
        print("Usage: python simple_keno_predictor.py START_DATE END_DATE [MIN_PERIODS]")
        print("Example: python simple_keno_predictor.py 2025-04-01 2025-04-30")
        print("Example: python simple_keno_predictor.py 2025-04-01 2025-04-30 30")
        sys.exit(1)

    start_date = sys.argv[1]
    end_date = sys.argv[2]
    min_periods = int(sys.argv[3]) if len(sys.argv) == 4 else 50

    test_range(start_date, end_date, min_periods)
