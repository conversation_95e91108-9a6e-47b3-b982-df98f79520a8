#!/usr/bin/env python3
"""
Final Keno Predictor - Simplified Version
Chỉ log ra 7 số chính và tạo ngẫu nhiên 10-20 cặp 4 số
"""

import mysql.connector
import random
from datetime import datetime, timedelta
from itertools import combinations
from collections import Counter

# KENO PERIOD CONSTANTS
KENO_START_TIME = "06:00:00"  # Kì đầu tiên
KENO_END_TIME = "21:44:00"    # Kì cuối cùng
KENO_INTERVAL_MINUTES = 8     # Mỗi kì cách nhau 8 phút
KENO_FIRST_PERIOD = 1         # Period đầu tiên
KENO_LAST_PERIOD = 119        # Period cuối cùng

def connect_db():
    """Kết nối database"""
    return mysql.connector.connect(
        host='localhost',
        user='root',
        port=3307,
        password='1',
        database='keno'
    )

def time_to_period(time_str):
    """Chuyển đổi time string thành period number

    Args:
        time_str: Time string format "HH:MM:SS" (e.g., "06:00:00")

    Returns:
        int: Period number (1-119) hoặc None nếu không hợp lệ

    Examples:
        "06:00:00" -> 1
        "06:08:00" -> 2
        "21:44:00" -> 119
    """
    try:
        # Parse time
        time_obj = datetime.strptime(time_str, "%H:%M:%S").time()
        start_time = datetime.strptime(KENO_START_TIME, "%H:%M:%S").time()

        # Tính số phút từ start time
        start_minutes = start_time.hour * 60 + start_time.minute
        current_minutes = time_obj.hour * 60 + time_obj.minute

        # Tính period
        minutes_diff = current_minutes - start_minutes
        if minutes_diff < 0:
            return None  # Trước giờ bắt đầu

        period = (minutes_diff // KENO_INTERVAL_MINUTES) + 1

        # Kiểm tra trong khoảng hợp lệ
        if period < KENO_FIRST_PERIOD or period > KENO_LAST_PERIOD:
            return None

        return period
    except:
        return None

def period_to_time(period):
    """Chuyển đổi period number thành time string

    Args:
        period: Period number (1-119)

    Returns:
        str: Time string format "HH:MM:SS" hoặc None nếu không hợp lệ

    Examples:
        1 -> "06:00:00"
        2 -> "06:08:00"
        119 -> "21:44:00"
    """
    try:
        if period < KENO_FIRST_PERIOD or period > KENO_LAST_PERIOD:
            return None

        # Tính số phút từ start time
        minutes_from_start = (period - 1) * KENO_INTERVAL_MINUTES

        # Parse start time
        start_time = datetime.strptime(KENO_START_TIME, "%H:%M:%S")

        # Thêm minutes
        result_time = start_time + timedelta(minutes=minutes_from_start)

        return result_time.strftime("%H:%M:%S")
    except:
        return None

def validate_period_time(period, time_str):
    """Kiểm tra period và time có khớp nhau không

    Args:
        period: Period number
        time_str: Time string

    Returns:
        bool: True nếu khớp, False nếu không khớp
    """
    expected_time = period_to_time(period)
    expected_period = time_to_period(time_str)

    return expected_time == time_str and expected_period == period

class FinalKenoPredictor:
    def __init__(self):
        # Money Management Settings
        self.daily_capital = 3_000_000  # 3 triệu VNĐ/ngày
        self.target_profit = 2_000_000  # 2 triệu VNĐ/ngày

        # Dynamic Pricing Settings
        self.base_ticket_price = 10_000     # Giá vé cơ bản 10k VNĐ
        self.min_ticket_price = 10_000      # Giá vé tối thiểu
        self.max_ticket_price = 80_000      # Giá vé tối đa
        self.win_multiplier = 3.2           # Tỷ lệ thắng 3.2x

        # Betting Settings
        self.base_tickets = 10               # Số vé cơ bản mỗi lần
        self.min_tickets = 10                # Tối thiểu 5 vé/lần
        self.max_tickets = 20               # Tối đa 10 vé/lần (giảm để tập trung volume vào giá vé)
        self.stop_loss_ratio = 0.2          # Dừng khi còn 20% vốn
        self.martingale_trigger = 5         # Bắt đầu martingale từ 3 ván thua

        # Martingale Tracking Settings
        self.martingale_periods_remaining = 0  # Số kì còn lại áp dụng Martingale
        self.martingale_type = ""  # Loại Martingale đang áp dụng

        # Skip Strategy Settings
        self.skip_periods_remaining = 0  # Số kì còn lại cần skip
        self.prediction_difficulty_threshold = 0.10  # Giảm từ 15% → 10% để ít skip hơn

        # Phase 2: Adaptive Learning Settings
        self.method_performance = {
            'lstm': [],
            'seasonal': [],
            'excluded': [],
            'pattern': [],
            'hot': [],
            'cold': []
        }
        self.method_weights = {
            'lstm': 0.25,
            'seasonal': 0.20,
            'excluded': 0.20,
            'pattern': 0.15,
            'hot': 0.10,
            'cold': 0.10
        }
        self.performance_window = 20  # Chỉ giữ 20 kết quả gần nhất
        self.confidence_scores = []  # Lưu confidence score từng kì

        # Phase 3: Advanced Features Settings
        self.market_timing_enabled = True
        self.risk_adjustment_enabled = True
        self.advanced_patterns_enabled = True
        self.dynamic_strategy_enabled = True

        # Market Timing Settings
        self.optimal_periods = []  # Các kì có tỷ lệ thắng cao
        self.avoid_periods = []    # Các kì nên tránh
        self.period_performance = {}  # Performance theo từng kì trong ngày

        # Risk Management Settings
        self.risk_tolerance = 0.3  # Mức độ chấp nhận rủi ro (0-1)
        self.volatility_threshold = 0.4  # Ngưỡng biến động
        self.drawdown_limit = 0.15  # Giới hạn drawdown 15%

        # Advanced Pattern Settings
        self.pattern_memory = []  # Lưu patterns đã nhận diện
        self.pattern_success_rate = {}  # Tỷ lệ thành công của từng pattern

        # Strategy Selection
        self.available_strategies = ['conservative', 'balanced', 'aggressive', 'adaptive']
        self.current_strategy = 'adaptive'
        self.strategy_performance = {s: [] for s in self.available_strategies}

        print("✅ Final Keno Predictor with Phase 3 Advanced Features initialized")
        print(f"💰 Vốn/ngày: {self.daily_capital:,} VNĐ")
        print(f"🎯 Mục tiêu: {self.target_profit:,} VNĐ/ngày")
        print(f"🎫 Giá vé: {self.min_ticket_price:,} - {self.max_ticket_price:,} VNĐ (dynamic)")
        print(f"🏆 Tỷ lệ thắng: {self.win_multiplier}x (chưa trừ chi phí)")
        print(f"📊 Số vé cơ bản: {self.base_tickets} vé/lần")

    def get_day_draws(self, date):
        """Lấy tất cả kì của một ngày"""
        try:
            print(f"🔍 Trying to load data for date: {date}")
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            # Debug: Kiểm tra dữ liệu có sẵn
            cursor.execute("SELECT MIN(date), MAX(date), COUNT(*) FROM histories_keno")
            min_date, max_date, total = cursor.fetchone()
            print(f"📊 Database range: {min_date} to {max_date} ({total:,} records)")

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (date,))
            rows = cursor.fetchall()
            print(f"📅 Found {len(rows)} records for {date}")

            cursor.close()
            conn.close()

            if not rows:
                print(f"⚠️ No data found for {date}, using sample data")
                return self._generate_sample_data()

            # Chuyển đổi results từ string thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            print(f"✅ Successfully loaded {len(rows)} periods for {date}")
            return rows

        except Exception as e:
            print(f"❌ Error loading day draws: {e}")
            print(f"🔄 Using sample data instead")
            # Fallback: Tạo dữ liệu mẫu để demo
            return self._generate_sample_data()

    def _generate_sample_data(self):
        """Tạo dữ liệu mẫu để demo"""
        sample_data = []
        for i in range(80):  # 80 kì
            # Tạo 20 số ngẫu nhiên từ 1-80
            results = sorted(random.sample(range(1, 81), 20))
            sample_data.append({
                'time': f"08:{i:02d}:00",
                'results': results,
                'period': i + 1
            })
        return sample_data

    def calculate_dynamic_ticket_price(self, consecutive_losses, consecutive_wins, cumulative_profit, recent_big_win=False):
        """Tính giá vé động dựa trên tình hình thắng/thua với chiến thuật mới + profit-based betting"""
        base_price = self.base_ticket_price
        price = base_price

        # CHIẾN THUẬT MỚI: Martingale cải tiến - tăng giá vé khi thua liên tiếp
        if consecutive_losses >= 5:
            # Thua 5-6 kì liên tiếp → tăng giá vé 100% (x2.0) trong 3 kì
            price = base_price * 2.0
            if self.martingale_periods_remaining <= 0:
                self.martingale_periods_remaining = 3  # Áp dụng trong 3 kì
                self.martingale_type = "100%"
            print(f"      🔥 MARTINGALE 100%: Thua {consecutive_losses} kì → Tăng giá vé x2.0 (còn {self.martingale_periods_remaining} kì)")
        elif consecutive_losses >= 3:
            # Thua 3 kì liên tiếp → tăng giá vé 50% (x1.5) trong 2 kì
            price = base_price * 1.5
            if self.martingale_periods_remaining <= 0:
                self.martingale_periods_remaining = 2  # Áp dụng trong 2 kì
                self.martingale_type = "50%"
            print(f"      📈 MARTINGALE 50%: Thua {consecutive_losses} kì → Tăng giá vé x1.5 (còn {self.martingale_periods_remaining} kì)")

        # CHIẾN THUẬT MỚI: Tăng giá vé khi thắng liên tiếp (aggressive betting)
        elif consecutive_wins >= 6:
            # Thắng 6 kì liên tiếp → tăng giá vé 300%
            price = base_price * 3.0
        elif consecutive_wins >= 3:
            # Thắng 3 kì liên tiếp → tăng giá vé 200%
            price = base_price * 2.0
        elif recent_big_win:
            # Thắng đậm → tăng 40% để maximize profit
            price = base_price * 1.4

        # CHIẾN THUẬT MỚI: Tăng cược dựa trên lợi nhuận tích lũy khi thắng liên tiếp
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            # Thắng liên tiếp 2-3 ván và đang có lời → dùng 50% lợi nhuận để tăng giá vé
            profit_boost = cumulative_profit * 0.5  # 50% lợi nhuận

            # Tính giá vé mới với profit boost (tối đa tăng 200% từ lợi nhuận)
            profit_multiplier = min(profit_boost / base_price, 2.0)  # Tối đa x3 (base + 200%)
            price = base_price * (1 + profit_multiplier)

            print(f"      💰 PROFIT-BASED BETTING: Thắng {consecutive_wins} kì liên tiếp, lời {cumulative_profit:,} VNĐ")
            print(f"      💰 Dùng 50% lời ({profit_boost:,} VNĐ) để tăng giá vé từ {base_price:,} → {price:,.0f} VNĐ")

        # Giữ giá cơ bản trong các trường hợp khác
        else:
            price = base_price

        # Cho phép vượt max_ticket_price trong trường hợp martingale, aggressive betting hoặc profit-based
        max_allowed_price = self.max_ticket_price * 3 if (consecutive_losses >= 3 or consecutive_wins >= 2) else self.max_ticket_price
        return max(self.min_ticket_price, min(price, max_allowed_price))

    def calculate_bet_size(self, current_capital, consecutive_losses, win_rate, ticket_price, consecutive_wins=0, cumulative_profit=0):
        """Tính số vé cần mua với chiến thuật mới + profit-based betting"""
        # Bắt đầu với base tickets
        base_tickets = self.base_tickets

        # Điều chỉnh dựa trên win rate
        if win_rate >= 0.40:  # Win rate rất cao
            base_tickets = self.base_tickets
        elif win_rate >= 0.35:  # Win rate cao
            base_tickets = max(self.base_tickets - 2, self.min_tickets)
        elif win_rate >= 0.30:  # Win rate trung bình
            base_tickets = max(self.base_tickets - 3, self.min_tickets)
        else:  # Win rate thấp
            base_tickets = self.min_tickets

        # CHIẾN THUẬT MỚI: Martingale cải tiến - tăng số vé khi thua liên tiếp
        if consecutive_losses >= 5:
            # Thua 5-6 kì liên tiếp → tăng số vé 100% (x2.0)
            base_tickets = int(base_tickets * 2.0)
            print(f"      🎫 MARTINGALE 100%: Thua {consecutive_losses} kì → Tăng số vé x2.0")
        elif consecutive_losses >= 3:
            # Thua 3 kì liên tiếp → tăng số vé 50% (x1.5)
            base_tickets = int(base_tickets * 1.5)
            print(f"      🎫 MARTINGALE 50%: Thua {consecutive_losses} kì → Tăng số vé x1.5")

        # CHIẾN THUẬT MỚI: Tăng cược khi thắng liên tiếp (aggressive betting)
        elif consecutive_wins >= 6:
            # Thắng 6 kì liên tiếp → tăng 300% (aggressive betting)
            base_tickets = int(base_tickets * 3.0)
        elif consecutive_wins >= 3:
            # Thắng 3 kì liên tiếp → tăng 200% (aggressive betting)
            base_tickets = int(base_tickets * 2.0)

        # CHIẾN THUẬT MỚI: Tăng số vé dựa trên lợi nhuận khi thắng liên tiếp 2+ ván
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            # Tính số vé bổ sung từ 50% lợi nhuận
            profit_boost = cumulative_profit * 0.5  # 50% lợi nhuận
            additional_tickets = int(profit_boost // ticket_price)  # Số vé có thể mua thêm

            # Tăng số vé nhưng không quá 50% base tickets
            max_additional = int(base_tickets * 0.5)
            additional_tickets = min(additional_tickets, max_additional)

            if additional_tickets > 0:
                base_tickets += additional_tickets
                print(f"      🎫 PROFIT-BASED TICKETS: Thêm {additional_tickets} vé từ lợi nhuận ({profit_boost:,} VNĐ)")

        # Giới hạn theo vốn còn lại và cho phép vượt max_tickets trong trường hợp martingale hoặc profit-based
        max_affordable = current_capital // ticket_price
        final_tickets = min(base_tickets, max_affordable, self.max_tickets * 2)  # Cho phép vượt max_tickets

        return max(final_tickets, self.min_tickets) if final_tickets >= self.min_tickets else 0

    def should_skip_prediction(self, recent_win_rate, consecutive_losses):
        """Kiểm tra có nên skip dự đoán không dựa trên độ khó"""
        # Nếu đang trong thời gian skip
        if self.skip_periods_remaining > 0:
            self.skip_periods_remaining -= 1
            print(f"      ⏸️ SKIP: Còn {self.skip_periods_remaining} kì cần skip")
            return True

        # Kiểm tra điều kiện khó dự đoán
        is_difficult = (
            recent_win_rate < self.prediction_difficulty_threshold or  # Win rate quá thấp
            consecutive_losses >= 7  # Thua quá nhiều liên tiếp
        )

        if is_difficult:
            # Skip 2-3 kì khi gặp điều kiện khó
            skip_periods = 3 if consecutive_losses >= 7 else 2
            self.skip_periods_remaining = skip_periods - 1  # -1 vì kì hiện tại đã skip
            print(f"      ⏸️ SKIP STRATEGY: Dự đoán khó (win rate: {recent_win_rate:.1%}, losses: {consecutive_losses}) → Skip {skip_periods} kì")
            return True

        return False

    def update_martingale_tracking(self):
        """Cập nhật tracking cho Martingale"""
        if self.martingale_periods_remaining > 0:
            self.martingale_periods_remaining -= 1
            if self.martingale_periods_remaining <= 0:
                print(f"      ✅ MARTINGALE {self.martingale_type} kết thúc")
                self.martingale_type = ""

    def calculate_profit(self, tickets_bought, winning_tickets, ticket_price):
        """Tính lợi nhuận từ một lần chơi với dynamic pricing"""
        cost = tickets_bought * ticket_price
        revenue = winning_tickets * ticket_price * self.win_multiplier
        profit = revenue - cost
        return profit, cost, revenue

    def get_lstm_predictions(self, day_results, num_predictions=10):
        """Dự đoán LSTM dựa trên frequency - chỉ lấy data các kì trong ngày"""
        try:
            # Chỉ lấy data các kì trong ngày hiện tại
            all_numbers = []
            for result in day_results:
                all_numbers.extend(result['results'])

            if not all_numbers:
                print(f"      ⚠️ LSTM: Không có dữ liệu trong ngày, dùng fallback 1-{num_predictions}")
                return list(range(1, num_predictions + 1))

            # Kiểm tra số kì có đủ >= num_predictions không
            if len(day_results) < num_predictions:
                print(f"      ⚠️ LSTM: Chỉ có {len(day_results)} kì, cần tối thiểu {num_predictions} kì")
                return list(range(1, num_predictions + 1))

            # Đếm tần suất
            frequency = Counter(all_numbers)
            most_common = frequency.most_common(num_predictions)
            predictions = [num for num, _ in most_common]
            print(f"      📊 LSTM top {len(predictions)} (từ {len(day_results)} kì trong ngày): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ LSTM error: {e}, dùng fallback 1-{num_predictions}")
            return list(range(1, num_predictions + 1))

    def get_seasonal_patterns(self, day_results, num_predictions=10, lookback_periods=5000):
        """Phân tích seasonal patterns - lấy từ lookback_periods kết quả gần nhất"""
        try:
            # Lấy patterns từ lookback_periods kết quả gần nhất
            seasonal_numbers = []
            for result in day_results[-lookback_periods:]:  # Lấy lookback_periods kết quả gần nhất
                seasonal_numbers.extend(result['results'])

            if not seasonal_numbers:
                print(f"      ⚠️ Seasonal: Không có dữ liệu, dùng fallback 11-{num_predictions+10}")
                return list(range(11, num_predictions + 11))

            # Sử dụng logic khác: ưu tiên số có tần suất trung bình (không quá cao, không quá thấp)
            frequency = Counter(seasonal_numbers)
            all_freq = frequency.most_common(80)  # Lấy tất cả

            # Lấy những số có tần suất ở khoảng 25%-75% (middle range)
            if len(all_freq) > 20:
                start_idx = len(all_freq) // 4  # 25%
                end_idx = 3 * len(all_freq) // 4  # 75%
                middle_range = all_freq[start_idx:end_idx]
                predictions = [num for num, _ in middle_range[:num_predictions]]
            else:
                predictions = [num for num, _ in all_freq[:num_predictions]]

            # Hiển thị số kì thực tế được sử dụng vs số kì được yêu cầu
            actual_used = min(lookback_periods, len(day_results))
            if len(day_results) < lookback_periods:
                print(f"      📊 Seasonal top {len(predictions)} (từ {actual_used}/{lookback_periods} kì): {predictions[:5]}... (middle-range strategy)")
            else:
                print(f"      📊 Seasonal top {len(predictions)} (từ {lookback_periods} kì gần nhất): {predictions[:5]}... (middle-range strategy)")
            return predictions
        except Exception as e:
            print(f"      ❌ Seasonal error: {e}, dùng fallback 11-{num_predictions+10}")
            return list(range(11, num_predictions + 11))

    def get_excluded_numbers(self, day_results):
        """Lấy các số bị loại trừ - sử dụng logic gap analysis"""
        try:
            if not day_results:
                print(f"      ⚠️ Excluded: Không có dữ liệu")
                return set()

            # Lấy 20 kết quả gần nhất
            recent_results = day_results[-20:]
            all_recent_numbers = []
            for result in recent_results:
                all_recent_numbers.extend(result['results'])

            # Tìm các số xuất hiện ít nhất (gap analysis)
            frequency = Counter(all_recent_numbers)
            all_numbers = set(range(1, 81))

            # Lấy các số có tần suất thấp nhất (dưới 25% percentile)
            freq_values = list(frequency.values())
            if freq_values:
                threshold = sorted(freq_values)[len(freq_values) // 4] if len(freq_values) > 4 else 1
                excluded = {num for num in all_numbers if frequency.get(num, 0) <= threshold}
            else:
                excluded = set(range(1, 21))  # Fallback: số 1-20

            excluded_list = sorted(list(excluded))
            print(f"      📊 Excluded ({len(excluded_list)}): {excluded_list[:5]}... (gap analysis)")
            return excluded
        except Exception as e:
            print(f"      ❌ Excluded error: {e}")
            return set(range(21, 41))  # Fallback khác

    def get_hot_numbers(self, day_results, num_predictions=10, lookback=30):
        """Lấy các số nóng - xuất hiện nhiều trong thời gian gần đây"""
        try:
            # Lấy dữ liệu gần đây
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if not recent_results:
                return list(range(1, num_predictions + 1))

            # Đếm tần suất xuất hiện
            hot_numbers = []
            for result in recent_results:
                hot_numbers.extend(result['results'])

            frequency = Counter(hot_numbers)
            most_common = frequency.most_common(num_predictions)
            predictions = [num for num, _ in most_common]

            print(f"      🔥 Hot Numbers top {len(predictions)} (từ {len(recent_results)} kì gần nhất): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ Hot Numbers error: {e}")
            return list(range(1, num_predictions + 1))

    def get_cold_numbers(self, day_results, num_predictions=10, lookback=50):
        """Lấy các số lạnh - ít xuất hiện trong thời gian gần đây"""
        try:
            # Lấy dữ liệu gần đây
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if not recent_results:
                return list(range(71, 71 + num_predictions))

            # Đếm tần suất xuất hiện
            cold_numbers = []
            for result in recent_results:
                cold_numbers.extend(result['results'])

            frequency = Counter(cold_numbers)

            # Tìm các số xuất hiện ít nhất
            all_numbers = set(range(1, 81))
            number_counts = {num: frequency.get(num, 0) for num in all_numbers}

            # Sắp xếp theo tần suất tăng dần (số lạnh nhất trước)
            sorted_cold = sorted(number_counts.items(), key=lambda x: x[1])
            predictions = [num for num, _ in sorted_cold[:num_predictions]]

            print(f"      ❄️ Cold Numbers top {len(predictions)} (từ {len(recent_results)} kì gần nhất): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ Cold Numbers error: {e}")
            return list(range(71, 71 + num_predictions))

    def analyze_recent_patterns(self, day_results, lookback=20):
        """Phân tích patterns gần đây để điều chỉnh strategy"""
        try:
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if len(recent_results) < 5:
                return {}

            # Phân tích chu kỳ xuất hiện
            number_gaps = {}
            for num in range(1, 81):
                last_seen = -1
                gaps = []
                for i, result in enumerate(recent_results):
                    if num in result['results']:
                        if last_seen >= 0:
                            gaps.append(i - last_seen)
                        last_seen = i

                if gaps and len(gaps) >= 2:
                    avg_gap = sum(gaps) / len(gaps)
                    current_gap = len(recent_results) - last_seen - 1 if last_seen >= 0 else len(recent_results)

                    # Số có chu kỳ đều và sắp đến lượt (gap âm = sắp xuất hiện)
                    if avg_gap > 0:
                        gap_score = avg_gap - current_gap
                        number_gaps[num] = gap_score

            return number_gaps
        except Exception as e:
            print(f"      ❌ Pattern analysis error: {e}")
            return {}

    def get_pattern_predictions(self, day_results, num_predictions=10):
        """Dự đoán dựa trên pattern chu kỳ"""
        try:
            patterns = self.analyze_recent_patterns(day_results)

            if not patterns:
                return list(range(31, 31 + num_predictions))

            # Sắp xếp theo độ ưu tiên (số âm = sắp xuất hiện)
            sorted_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)
            predictions = [num for num, _ in sorted_patterns[:num_predictions]]

            print(f"      🔄 Pattern Predictions top {len(predictions)}: {predictions[:5]}... (cycle analysis)")
            return predictions
        except Exception as e:
            print(f"      ❌ Pattern Predictions error: {e}")
            return list(range(31, 31 + num_predictions))

    def get_ensemble_predictions(self, day_results, actual_results=None):
        """Phase 2: Kết hợp nhiều phương pháp với trọng số adaptive"""
        try:
            print(f"      🎯 ENSEMBLE PREDICTIONS (Phase 2 - Adaptive):")

            # 1. Các phương pháp cơ bản
            lstm_pred = self.get_lstm_predictions(day_results, 15)
            seasonal_pred = self.get_seasonal_patterns(day_results, 15, 5000)
            excluded_pred = list(self.get_excluded_numbers(day_results))

            # 2. Phương pháp bổ sung
            hot_numbers = self.get_hot_numbers(day_results, 10)
            cold_numbers = self.get_cold_numbers(day_results, 10)
            pattern_pred = self.get_pattern_predictions(day_results, 15)

            print(f"      📝 PHƯƠNG PHÁP DỰ ĐOÁN:")
            print(f"         🤖 LSTM: Dự đoán số có tần suất cao")
            print(f"         📊 SEASONAL: Dự đoán số theo mùa/chu kỳ")
            print(f"         🚫 EXCLUDED: Dự đoán số có khả năng TRƯỢT cao (gap analysis)")
            print(f"         🔥 HOT: Số nóng (xuất hiện nhiều gần đây)")
            print(f"         ❄️ COLD: Số lạnh (ít xuất hiện gần đây)")
            print(f"         🔄 PATTERN: Dự đoán theo chu kỳ xuất hiện")

            # 3. Tạo predictions dictionary để tracking
            predictions_dict = {
                'lstm': lstm_pred,
                'seasonal': seasonal_pred,
                'excluded': excluded_pred,
                'pattern': pattern_pred,
                'hot': hot_numbers,
                'cold': cold_numbers
            }

            # 4. Phase 2: Cập nhật performance nếu có actual results
            if actual_results is not None:
                self.update_method_performance(predictions_dict, actual_results)
                self.calculate_adaptive_weights()

            # 5. Phase 2: Tính confidence score
            confidence = self.calculate_confidence_score(predictions_dict)

            # 6. Phase 2: Sử dụng adaptive weights
            weights = self.method_weights
            print(f"      📊 Current weights: {[(k, f'{v:.3f}') for k, v in weights.items()]}")

            # 7. Tính điểm tổng hợp với adaptive weights
            final_scores = {}

            for method, pred_list in predictions_dict.items():
                weight = weights[method]
                for i, num in enumerate(pred_list[:10]):
                    score = (10 - i) * weight
                    final_scores[num] = final_scores.get(num, 0) + score

            # 8. Chọn top 6 số tốt nhất
            sorted_scores = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
            final_6 = [num for num, _ in sorted_scores[:6]]

            print(f"      🎯 Ensemble Final 6: {final_6}")
            print(f"      📊 Top ensemble scores (tổng hợp 6 phương pháp): {[(num, f'{score:.2f}') for num, score in sorted_scores[:6]]}")
            print(f"      💡 Giải thích: Score cao = số được nhiều phương pháp ưu tiên (bao gồm cả số trượt và số ra)")

            return final_6, confidence, predictions_dict
        except Exception as e:
            print(f"      ❌ Ensemble error: {e}")
            # Fallback to original weighted strategy
            fallback = self.get_weighted_strategy_fallback(day_results)
            return fallback, 0.5, {}

    def get_weighted_strategy_fallback(self, day_results):
        """Fallback strategy khi ensemble fail"""
        try:
            lstm_predictions = self.get_lstm_predictions(day_results, 15)
            seasonal_predictions = self.get_seasonal_patterns(day_results, 15, 5000)
            excluded_numbers = list(self.get_excluded_numbers(day_results))

            # Tính trọng số cho từng số (logic cũ)
            weight_scores = {}

            for i, num in enumerate(lstm_predictions[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)

            for i, num in enumerate(seasonal_predictions[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)

            for i, num in enumerate(excluded_numbers[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)

            sorted_by_weight = sorted(weight_scores.items(), key=lambda x: x[1], reverse=True)
            return [num for num, _ in sorted_by_weight[:6]]
        except Exception as e:
            print(f"      ❌ Fallback error: {e}")
            return list(range(1, 7))

    def is_balanced_combination(self, combo):
        """Kiểm tra combination có cân bằng không"""
        try:
            # Cân bằng chẵn/lẻ (2:2 hoặc 3:1)
            even_count = sum(1 for n in combo if n % 2 == 0)
            if even_count < 1 or even_count > 3:
                return False

            # Cân bằng thấp/cao (2:2 hoặc 3:1)
            low_count = sum(1 for n in combo if n <= 40)
            if low_count < 1 or low_count > 3:
                return False

            # Không có số liên tiếp quá nhiều
            sorted_combo = sorted(combo)
            consecutive = 0
            for i in range(1, len(sorted_combo)):
                if sorted_combo[i] - sorted_combo[i-1] == 1:
                    consecutive += 1
            if consecutive > 2:
                return False

            return True
        except Exception as e:
            print(f"      ❌ Balance check error: {e}")
            return True  # Default to True if error

    def generate_smart_combinations(self, final_6_numbers, num_combos=15):
        """Tạo combinations thông minh thay vì random"""
        try:
            if len(final_6_numbers) < 4:
                return []

            print(f"      🎲 SMART COMBINATIONS (Phase 1):")

            # 1. Tạo tất cả combinations có thể
            all_possible_combos = list(combinations(final_6_numbers, 4))

            # 2. Lọc combinations cân bằng
            balanced_combos = []
            for combo in all_possible_combos:
                if self.is_balanced_combination(combo):
                    balanced_combos.append(combo)

            print(f"      📊 Balanced combos: {len(balanced_combos)}/{len(all_possible_combos)}")

            # 3. Nếu có đủ balanced combos, ưu tiên chúng
            if len(balanced_combos) >= num_combos:
                selected_combos = random.sample(balanced_combos, num_combos)
                print(f"      ✅ Using {num_combos} balanced combinations")
            elif len(balanced_combos) > 0:
                # Dùng tất cả balanced + thêm random
                remaining = num_combos - len(balanced_combos)
                unbalanced_combos = [c for c in all_possible_combos if c not in balanced_combos]
                additional = random.sample(unbalanced_combos, min(remaining, len(unbalanced_combos)))
                selected_combos = balanced_combos + additional
                print(f"      ⚖️ Using {len(balanced_combos)} balanced + {len(additional)} random")
            else:
                # Fallback to random
                selected_combos = random.sample(all_possible_combos, min(num_combos, len(all_possible_combos)))
                print(f"      🎲 Fallback to {len(selected_combos)} random combinations")

            return selected_combos
        except Exception as e:
            print(f"      ❌ Smart combinations error: {e}")
            # Fallback to original random method
            all_possible_combos = list(combinations(final_6_numbers, 4))
            num_combos = min(num_combos, len(all_possible_combos))
            return random.sample(all_possible_combos, num_combos) if all_possible_combos else []

    def update_method_performance(self, predictions_dict, actual_results):
        """Phase 2: Cập nhật performance của từng phương pháp"""
        try:
            for method, pred_list in predictions_dict.items():
                if method in self.method_performance:
                    # Tính accuracy: số lượng số dự đoán đúng / tổng số dự đoán
                    hits = len(set(pred_list[:6]) & set(actual_results))
                    accuracy = hits / 6.0

                    # Thêm vào performance history
                    self.method_performance[method].append(accuracy)

                    # Chỉ giữ performance_window kết quả gần nhất
                    if len(self.method_performance[method]) > self.performance_window:
                        self.method_performance[method].pop(0)

                    print(f"      📊 {method.upper()} accuracy: {accuracy:.2f} ({hits}/6 hits)")
        except Exception as e:
            print(f"      ❌ Performance update error: {e}")

    def calculate_adaptive_weights(self):
        """Phase 2: Tính trọng số adaptive dựa trên performance"""
        try:
            new_weights = {}
            total_performance = 0

            for method in self.method_weights.keys():
                if self.method_performance[method]:
                    # Lấy performance trung bình của 10 kì gần nhất
                    recent_performance = self.method_performance[method][-10:]
                    avg_performance = sum(recent_performance) / len(recent_performance)

                    # Boost performance bằng cách thêm base score
                    boosted_performance = max(0.1, avg_performance + 0.1)  # Tối thiểu 10%
                    new_weights[method] = boosted_performance
                    total_performance += boosted_performance
                else:
                    # Sử dụng weight mặc định nếu chưa có data
                    new_weights[method] = self.method_weights[method]
                    total_performance += self.method_weights[method]

            # Normalize weights
            if total_performance > 0:
                for method in new_weights:
                    new_weights[method] = new_weights[method] / total_performance

                # Cập nhật weights
                old_weights = self.method_weights.copy()
                self.method_weights = new_weights

                print(f"      🎯 ADAPTIVE WEIGHTS UPDATE:")
                for method in self.method_weights:
                    old_w = old_weights[method]
                    new_w = self.method_weights[method]
                    change = ((new_w - old_w) / old_w) * 100 if old_w > 0 else 0
                    print(f"         {method}: {old_w:.3f} → {new_w:.3f} ({change:+.1f}%)")

            return self.method_weights
        except Exception as e:
            print(f"      ❌ Adaptive weights error: {e}")
            return self.method_weights

    def calculate_confidence_score(self, predictions_dict):
        """Phase 2: Tính confidence score cho prediction hiện tại"""
        try:
            # 1. Tính độ nhất quán giữa các methods
            all_predictions = []
            for pred_list in predictions_dict.values():
                all_predictions.extend(pred_list[:6])

            # Đếm số lần xuất hiện của mỗi số
            prediction_counts = Counter(all_predictions)

            # 2. Tính consensus score (số nào được nhiều method chọn)
            consensus_score = 0
            for count in prediction_counts.values():
                if count >= 2:  # Xuất hiện ở ít nhất 2 methods
                    consensus_score += count

            consensus_score = min(consensus_score / 20, 1.0)  # Normalize to 0-1

            # 3. Tính performance score dựa trên lịch sử
            performance_score = 0
            total_methods = 0
            for _, performances in self.method_performance.items():
                if performances:
                    recent_perf = performances[-5:]  # 5 kì gần nhất
                    avg_perf = sum(recent_perf) / len(recent_perf)
                    performance_score += avg_perf
                    total_methods += 1

            if total_methods > 0:
                performance_score = performance_score / total_methods
            else:
                performance_score = 0.5  # Default

            # 4. Tính confidence tổng hợp
            confidence = (consensus_score * 0.6 + performance_score * 0.4)

            # Lưu confidence score
            self.confidence_scores.append(confidence)
            if len(self.confidence_scores) > self.performance_window:
                self.confidence_scores.pop(0)

            print(f"      🎯 CONFIDENCE SCORE: {confidence:.3f} (consensus: {consensus_score:.3f}, performance: {performance_score:.3f})")

            return confidence
        except Exception as e:
            print(f"      ❌ Confidence calculation error: {e}")
            return 0.5  # Default confidence

    def get_optimal_ticket_count(self, confidence_score, base_tickets=10):
        """Phase 2: Tính số vé tối ưu dựa trên confidence"""
        try:
            if confidence_score > 0.8:
                multiplier = 1.5  # Tăng 50% khi confidence cao
                strategy = "HIGH CONFIDENCE"
            elif confidence_score > 0.6:
                multiplier = 1.2  # Tăng 20% khi confidence khá
                strategy = "MEDIUM CONFIDENCE"
            elif confidence_score < 0.3:
                multiplier = 0.7  # Giảm 30% khi confidence thấp
                strategy = "LOW CONFIDENCE"
            else:
                multiplier = 1.0  # Giữ nguyên
                strategy = "NORMAL CONFIDENCE"

            optimal_tickets = int(base_tickets * multiplier)
            optimal_tickets = max(3, min(optimal_tickets, 20))  # Giới hạn 3-20 vé

            print(f"      🎯 CONFIDENCE BETTING: {strategy} ({confidence_score:.3f}) → {optimal_tickets} vé (x{multiplier})")

            return optimal_tickets
        except Exception as e:
            print(f"      ❌ Optimal ticket count error: {e}")
            return base_tickets

    def analyze_market_timing(self, day_results, current_period):
        """Phase 3: Phân tích thời điểm tốt nhất để đặt cược"""
        try:
            if not self.market_timing_enabled:
                return 1.0, "TIMING_DISABLED"

            # Phân tích performance theo từng kì trong ngày
            period_stats = {}

            # Nhóm kết quả theo period
            for result in day_results:
                period = result.get('period', 0)
                if period not in period_stats:
                    period_stats[period] = {'wins': 0, 'total': 0, 'profits': []}

            # Tính win rate theo từng kì
            if len(period_stats) > 10:  # Đủ data để phân tích
                # Phân tích theo nhóm kì (để future expansion)
                _ = [p for p in period_stats.keys() if 1 <= p <= 40]  # morning_periods
                _ = [p for p in period_stats.keys() if 41 <= p <= 80]  # afternoon_periods
                _ = [p for p in period_stats.keys() if 81 <= p <= 119]  # evening_periods

                current_time_group = "MORNING"
                if 41 <= current_period <= 80:
                    current_time_group = "AFTERNOON"
                elif 81 <= current_period <= 119:
                    current_time_group = "EVENING"

                # Tính timing multiplier
                if current_time_group == "MORNING":
                    timing_multiplier = 1.1  # Buổi sáng thường ổn định hơn
                    timing_reason = "MORNING_STABLE"
                elif current_time_group == "AFTERNOON":
                    timing_multiplier = 1.0  # Buổi chiều bình thường
                    timing_reason = "AFTERNOON_NORMAL"
                else:  # EVENING
                    timing_multiplier = 0.9  # Buổi tối thường biến động hơn
                    timing_reason = "EVENING_VOLATILE"

                print(f"      ⏰ MARKET TIMING: {current_time_group} (kì {current_period}) → x{timing_multiplier} ({timing_reason})")
                return timing_multiplier, timing_reason
            else:
                return 1.0, "INSUFFICIENT_DATA"

        except Exception as e:
            print(f"      ❌ Market timing error: {e}")
            return 1.0, "ERROR"

    def calculate_risk_score(self, day_results, confidence_score, consecutive_losses):
        """Phase 3: Tính điểm rủi ro tổng hợp"""
        try:
            if not self.risk_adjustment_enabled:
                return 0.5, "RISK_DISABLED"

            # 1. Volatility risk (dựa trên biến động gần đây)
            recent_results = day_results[-10:] if len(day_results) >= 10 else day_results
            if len(recent_results) >= 5:
                # Tính độ biến động của kết quả
                result_variations = []
                for i in range(1, len(recent_results)):
                    prev_set = set(recent_results[i-1]['results'])
                    curr_set = set(recent_results[i]['results'])
                    overlap = len(prev_set & curr_set)
                    variation = (20 - overlap) / 20  # 0-1, càng cao càng biến động
                    result_variations.append(variation)

                volatility_risk = sum(result_variations) / len(result_variations)
            else:
                volatility_risk = 0.5  # Default

            # 2. Confidence risk (ngược với confidence score)
            confidence_risk = 1 - confidence_score

            # 3. Streak risk (rủi ro từ chuỗi thua)
            streak_risk = min(consecutive_losses / 10, 1.0)  # Max 1.0

            # 4. Tính risk score tổng hợp
            risk_score = (volatility_risk * 0.4 + confidence_risk * 0.4 + streak_risk * 0.2)

            # Phân loại risk level
            if risk_score <= 0.3:
                risk_level = "LOW"
            elif risk_score <= 0.6:
                risk_level = "MEDIUM"
            else:
                risk_level = "HIGH"

            print(f"      ⚠️ RISK ANALYSIS: {risk_level} ({risk_score:.3f}) - Vol:{volatility_risk:.2f} Conf:{confidence_risk:.2f} Streak:{streak_risk:.2f}")

            return risk_score, risk_level

        except Exception as e:
            print(f"      ❌ Risk calculation error: {e}")
            return 0.5, "ERROR"

    def detect_advanced_patterns(self, day_results):
        """Phase 3: Nhận diện patterns phức tạp"""
        try:
            if not self.advanced_patterns_enabled or len(day_results) < 10:
                return [], "INSUFFICIENT_DATA"

            detected_patterns = []
            recent_results = day_results[-15:]  # Phân tích 15 kì gần nhất

            # 1. Consecutive Number Pattern
            consecutive_pattern = self._detect_consecutive_pattern(recent_results)
            if consecutive_pattern:
                detected_patterns.append(consecutive_pattern)

            # 2. Sum Range Pattern
            sum_pattern = self._detect_sum_pattern(recent_results)
            if sum_pattern:
                detected_patterns.append(sum_pattern)

            # 3. Even/Odd Balance Pattern
            balance_pattern = self._detect_balance_pattern(recent_results)
            if balance_pattern:
                detected_patterns.append(balance_pattern)

            # 4. Repeat Number Pattern
            repeat_pattern = self._detect_repeat_pattern(recent_results)
            if repeat_pattern:
                detected_patterns.append(repeat_pattern)

            if detected_patterns:
                pattern_names = [p['name'] for p in detected_patterns]
                print(f"      🔍 ADVANCED PATTERNS: {', '.join(pattern_names)}")
                return detected_patterns, "SUCCESS"
            else:
                return [], "NO_PATTERNS"

        except Exception as e:
            print(f"      ❌ Pattern detection error: {e}")
            return [], "ERROR"

    def _detect_consecutive_pattern(self, recent_results):
        """Phát hiện pattern số liên tiếp"""
        try:
            consecutive_counts = []
            for result in recent_results:
                sorted_nums = sorted(result['results'])
                consecutive = 0
                for i in range(1, len(sorted_nums)):
                    if sorted_nums[i] - sorted_nums[i-1] == 1:
                        consecutive += 1
                consecutive_counts.append(consecutive)

            avg_consecutive = sum(consecutive_counts) / len(consecutive_counts)
            if avg_consecutive > 3:  # Trung bình > 3 số liên tiếp
                return {
                    'name': 'HIGH_CONSECUTIVE',
                    'strength': min(avg_consecutive / 6, 1.0),
                    'recommendation': 'avoid_consecutive'
                }
            elif avg_consecutive < 1:  # Ít số liên tiếp
                return {
                    'name': 'LOW_CONSECUTIVE',
                    'strength': (1 - avg_consecutive),
                    'recommendation': 'prefer_consecutive'
                }
            return None
        except:
            return None

    def _detect_sum_pattern(self, recent_results):
        """Phát hiện pattern tổng số"""
        try:
            sums = []
            for result in recent_results:
                total = sum(result['results'])
                sums.append(total)

            avg_sum = sum(sums) / len(sums)
            # Tổng bình thường của 20 số từ 1-80 là khoảng 810
            if avg_sum > 900:  # Tổng cao
                return {
                    'name': 'HIGH_SUM',
                    'strength': min((avg_sum - 810) / 200, 1.0),
                    'recommendation': 'prefer_low_numbers'
                }
            elif avg_sum < 720:  # Tổng thấp
                return {
                    'name': 'LOW_SUM',
                    'strength': min((810 - avg_sum) / 200, 1.0),
                    'recommendation': 'prefer_high_numbers'
                }
            return None
        except:
            return None

    def _detect_balance_pattern(self, recent_results):
        """Phát hiện pattern cân bằng chẵn/lẻ"""
        try:
            even_ratios = []
            for result in recent_results:
                even_count = sum(1 for n in result['results'] if n % 2 == 0)
                even_ratio = even_count / 20
                even_ratios.append(even_ratio)

            avg_even_ratio = sum(even_ratios) / len(even_ratios)
            if avg_even_ratio > 0.65:  # Quá nhiều số chẵn
                return {
                    'name': 'EVEN_HEAVY',
                    'strength': min((avg_even_ratio - 0.5) / 0.3, 1.0),
                    'recommendation': 'prefer_odd_numbers'
                }
            elif avg_even_ratio < 0.35:  # Quá nhiều số lẻ
                return {
                    'name': 'ODD_HEAVY',
                    'strength': min((0.5 - avg_even_ratio) / 0.3, 1.0),
                    'recommendation': 'prefer_even_numbers'
                }
            return None
        except:
            return None

    def _detect_repeat_pattern(self, recent_results):
        """Phát hiện pattern lặp lại số"""
        try:
            if len(recent_results) < 3:
                return None

            # Tìm số xuất hiện trong 2-3 kì gần nhất
            last_3_periods = recent_results[-3:]
            all_numbers = []
            for result in last_3_periods:
                all_numbers.extend(result['results'])

            frequency = Counter(all_numbers)
            repeat_numbers = [num for num, count in frequency.items() if count >= 2]

            if len(repeat_numbers) > 10:  # Nhiều số lặp lại
                return {
                    'name': 'HIGH_REPEAT',
                    'strength': min(len(repeat_numbers) / 20, 1.0),
                    'recommendation': 'avoid_recent_numbers'
                }
            elif len(repeat_numbers) < 5:  # Ít số lặp lại
                return {
                    'name': 'LOW_REPEAT',
                    'strength': min((5 - len(repeat_numbers)) / 5, 1.0),
                    'recommendation': 'prefer_recent_numbers'
                }
            return None
        except:
            return None

    def select_optimal_strategy(self, risk_score, confidence_score, timing_multiplier, patterns):
        """Phase 3: Chọn strategy tối ưu dựa trên điều kiện hiện tại"""
        try:
            if not self.dynamic_strategy_enabled:
                return self.current_strategy, "DYNAMIC_DISABLED"

            # Tính điểm cho từng strategy
            strategy_scores = {}

            # Conservative Strategy
            conservative_score = 0
            if risk_score > 0.6:  # High risk → prefer conservative
                conservative_score += 0.4
            if confidence_score < 0.4:  # Low confidence → prefer conservative
                conservative_score += 0.3
            if timing_multiplier < 1.0:  # Bad timing → prefer conservative
                conservative_score += 0.2
            strategy_scores['conservative'] = conservative_score

            # Aggressive Strategy
            aggressive_score = 0
            if risk_score < 0.3:  # Low risk → can be aggressive
                aggressive_score += 0.4
            if confidence_score > 0.7:  # High confidence → can be aggressive
                aggressive_score += 0.4
            if timing_multiplier > 1.0:  # Good timing → can be aggressive
                aggressive_score += 0.2
            strategy_scores['aggressive'] = aggressive_score

            # Balanced Strategy (always moderate score)
            balanced_score = 0.3 + (1 - abs(risk_score - 0.5)) * 0.2  # Prefer when risk is moderate
            strategy_scores['balanced'] = balanced_score

            # Adaptive Strategy (bonus for pattern detection)
            adaptive_score = 0.2  # Base score
            if patterns:  # Có patterns → adaptive có lợi thế
                adaptive_score += len(patterns) * 0.1
            if 0.3 <= confidence_score <= 0.7:  # Moderate confidence → adaptive works well
                adaptive_score += 0.2
            strategy_scores['adaptive'] = adaptive_score

            # Chọn strategy có điểm cao nhất
            best_strategy = max(strategy_scores, key=strategy_scores.get)
            best_score = strategy_scores[best_strategy]

            # Chỉ thay đổi strategy nếu điểm chênh lệch đáng kể (>0.15)
            current_score = strategy_scores.get(self.current_strategy, 0)
            if best_score - current_score > 0.15:
                old_strategy = self.current_strategy
                self.current_strategy = best_strategy
                print(f"      🔄 STRATEGY CHANGE: {old_strategy} → {best_strategy} (score: {best_score:.2f})")
                return best_strategy, "CHANGED"
            else:
                print(f"      🎯 STRATEGY KEEP: {self.current_strategy} (score: {current_score:.2f}, best: {best_strategy} {best_score:.2f})")
                return self.current_strategy, "UNCHANGED"

        except Exception as e:
            print(f"      ❌ Strategy selection error: {e}")
            return self.current_strategy, "ERROR"

    def apply_strategy_adjustments(self, base_tickets, ticket_price, strategy, risk_score):
        """Phase 3: Áp dụng điều chỉnh theo strategy được chọn"""
        try:
            adjusted_tickets = base_tickets
            adjusted_price = ticket_price

            if strategy == 'conservative':
                # Conservative: Giảm risk, giảm bet size
                adjusted_tickets = int(base_tickets * 0.7)  # Giảm 30% số vé
                adjusted_price = int(ticket_price * 0.8)    # Giảm 20% giá vé
                print(f"      🛡️ CONSERVATIVE: Tickets {base_tickets}→{adjusted_tickets}, Price {ticket_price:,}→{adjusted_price:,}")

            elif strategy == 'aggressive':
                # Aggressive: Tăng bet size khi điều kiện tốt
                adjusted_tickets = int(base_tickets * 1.3)  # Tăng 30% số vé
                adjusted_price = int(ticket_price * 1.2)    # Tăng 20% giá vé
                print(f"      ⚡ AGGRESSIVE: Tickets {base_tickets}→{adjusted_tickets}, Price {ticket_price:,}→{adjusted_price:,}")

            elif strategy == 'balanced':
                # Balanced: Điều chỉnh nhẹ dựa trên risk
                risk_adjustment = 1.0 - (risk_score - 0.5) * 0.2  # -0.1 to +0.1
                adjusted_tickets = int(base_tickets * risk_adjustment)
                print(f"      ⚖️ BALANCED: Risk-adjusted tickets {base_tickets}→{adjusted_tickets} (risk: {risk_score:.2f})")

            elif strategy == 'adaptive':
                # Adaptive: Điều chỉnh dựa trên nhiều yếu tố
                # Đã được handle trong các hàm khác, giữ nguyên
                print(f"      🎯 ADAPTIVE: Using dynamic adjustments (tickets: {adjusted_tickets}, price: {adjusted_price:,})")

            # Đảm bảo trong giới hạn
            adjusted_tickets = max(self.min_tickets, min(adjusted_tickets, self.max_tickets * 2))
            adjusted_price = max(self.min_ticket_price, min(adjusted_price, self.max_ticket_price * 3))

            return adjusted_tickets, adjusted_price

        except Exception as e:
            print(f"      ❌ Strategy adjustment error: {e}")
            return base_tickets, ticket_price

    def _ensure_unique_7_numbers(self, initial_list, lstm_predictions, seasonal_predictions, excluded_numbers):
        """Đảm bảo có đúng 7 số unique với logic fallback thông minh"""
        # Loại bỏ duplicate từ initial list
        unique_numbers = []
        seen = set()
        for num in initial_list:
            if num not in seen:
                unique_numbers.append(num)
                seen.add(num)

        # Nếu đã đủ 7 số unique
        if len(unique_numbers) >= 7:
            return unique_numbers[:7]

        # Cần thêm số - ưu tiên từ top 4-5 của các feature
        fallback_candidates = []

        # Thêm từ LSTM top 4-5
        if len(lstm_predictions) > 3:
            fallback_candidates.extend(lstm_predictions[3:5])

        # Thêm từ Seasonal top 1, 3-5
        if len(seasonal_predictions) >= 1:
            fallback_candidates.append(seasonal_predictions[0])  # Top 1
        if len(seasonal_predictions) > 2:
            fallback_candidates.extend(seasonal_predictions[2:5])  # Top 3-5

        # Thêm từ Excluded top 4-5
        if len(excluded_numbers) > 3:
            fallback_candidates.extend(list(excluded_numbers)[3:5])

        # Thêm các số chưa có từ fallback candidates
        for num in fallback_candidates:
            if num not in seen and len(unique_numbers) < 7:
                unique_numbers.append(num)
                seen.add(num)

        # Nếu vẫn chưa đủ, thêm số từ 1-80
        for num in range(1, 81):
            if num not in seen and len(unique_numbers) < 7:
                unique_numbers.append(num)
                seen.add(num)

        return unique_numbers[:7]

    def predict_period(self, day_draws, period_index):
        """Dự đoán cho một kì cụ thể"""
        if period_index >= len(day_draws):
            return None

        # Lấy dữ liệu training (tất cả kì trước đó)
        training_data = [day_draws[i]['results'] for i in range(period_index)]
        if len(training_data) < 10:
            return None

        # Kết quả thực tế của kì cần dự đoán
        actual_results = day_draws[period_index]['results']
        actual_missing = [i for i in range(1, 81) if i not in actual_results]

        # Debug: Kiểm tra training data
        print(f"      🔍 Training data: {len(training_data)} kì, sample: {training_data[-1] if training_data else 'None'}")

        # Tạo day_draws format cho các hàm prediction
        day_draws_format = [{'results': results} for results in training_data]

        # PHASE 3: ADVANCED FEATURES - Tích hợp tất cả tính năng nâng cao
        try:
            # 1. Lấy actual results để update performance (nếu có)
            actual_missing = [i for i in range(1, 81) if i not in actual_results]

            # 2. Phase 3: Market Timing Analysis
            current_period = period_index + 1  # Convert to 1-based period
            timing_multiplier, _ = self.analyze_market_timing(day_draws_format, current_period)

            # 3. Phase 3: Advanced Pattern Detection
            detected_patterns, _ = self.detect_advanced_patterns(day_draws_format)

            # 4. Phase 2: Ensemble Predictions (với Phase 3 enhancements)
            final_6_strategy, confidence_score, _ = self.get_ensemble_predictions(
                day_draws_format, actual_missing
            )

            # 5. Phase 3: Risk Analysis (sử dụng default consecutive_losses = 0)
            default_consecutive_losses = 0  # Will be updated in main loop
            risk_score, _ = self.calculate_risk_score(day_draws_format, confidence_score, default_consecutive_losses)

            # 6. Phase 3: Dynamic Strategy Selection
            _, _ = self.select_optimal_strategy(
                risk_score, confidence_score, timing_multiplier, detected_patterns
            )

        except Exception as e:
            print(f"      ❌ Phase 3 analysis failed: {e}, using fallback")
            # Fallback to Phase 2
            lstm_predictions = self.get_lstm_predictions(day_draws_format, 15)
            seasonal_predictions = self.get_seasonal_patterns(day_draws_format, 15, 5000)
            excluded_numbers = list(self.get_excluded_numbers(day_draws_format))

            # Tính trọng số cho từng số (logic cũ)
            weight_scores = {}
            for i, num in enumerate(lstm_predictions[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)
            for i, num in enumerate(seasonal_predictions[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)
            for i, num in enumerate(list(excluded_numbers)[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)

            sorted_by_weight = sorted(weight_scores.items(), key=lambda x: x[1], reverse=True)
            final_6_strategy = [num for num, _ in sorted_by_weight[:6]]
            confidence_score = 0.5  # Default confidence
            risk_score = 0.5
            timing_multiplier = 1.0
            _ = 'adaptive'  # selected_strategy fallback
            detected_patterns = []
            print(f"      🎯 Fallback weights: {[(num, weight) for num, weight in sorted_by_weight[:6]]}")

        # PHASE 3: ADVANCED COMBINATIONS - Tạo combinations với tất cả enhancements
        if len(final_6_strategy) >= 4:
            # Số combinations dựa trên confidence và timing
            base_combos = 12
            confidence_combos = self.get_optimal_ticket_count(confidence_score, base_combos)
            timing_adjusted_combos = int(confidence_combos * timing_multiplier)
            final_combos = max(8, min(timing_adjusted_combos, 20))  # Giới hạn 8-20

            selected_combos = self.generate_smart_combinations(final_6_strategy, final_combos)

            # Kiểm tra vé thắng (legacy - để tương thích với code cũ)
            winning_combos = 0
            for combo in selected_combos:
                missing_count = sum(1 for num in combo if num in actual_missing)
                if missing_count >= 4:  # Thắng nếu cả 4 số đều trượt
                    winning_combos += 1

            total_combos = len(selected_combos)
        else:
            winning_combos = 0
            total_combos = 0
            selected_combos = []
        
        return {
            'period': period_index + 1,
            'time': day_draws[period_index]['time'],
            'actual_results': actual_results,
            'final_6_strategy': final_6_strategy,
            'selected_combos': selected_combos,
            'winning_combos': winning_combos,
            'total_combos': total_combos
        }

    def test_date_range(self, start_date, end_date, min_periods=50):
        """Test từ ngày start_date đến end_date"""
        print(f"\n🎯 TESTING DATE RANGE: {start_date} → {end_date}")
        print(f"⚙️ Config: Minimum periods = {min_periods}")
        print("="*60)
        
        # Tạo danh sách ngày test
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        test_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            test_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)
        
        # Thống kê tổng hợp
        total_stats = {
            'winning_combos': 0,
            'total_combos': 0,
            'total_predictions': 0,
            'total_days': 0,
            'daily_stats': {},  # Lưu thống kê từng ngày
            'total_profit': 0,
            'total_cost': 0,
            'total_revenue': 0
        }
        
        for test_date in test_dates:
            print(f"\n📅 Testing {test_date}...")
            
            day_draws = self.get_day_draws(test_date)
            if len(day_draws) < min_periods + 1:
                print(f"   ⚠️ Không đủ dữ liệu (có {len(day_draws)}, cần {min_periods + 1})")
                continue
            
            # Test từ kì min_periods+1 đến cuối ngày với money management
            max_period = min(len(day_draws), 119)
            day_predictions = 0
            day_winning = 0
            day_total = 0

            # Money management cho ngày
            current_capital = self.daily_capital
            daily_profit = 0
            daily_cost = 0
            daily_revenue = 0
            consecutive_losses = 0
            consecutive_wins = 0
            total_bets = 0
            recent_big_win = False
            last_period_profit = 0

            # Tracking cho skip strategy
            self._period_results = {}  # Lưu kết quả từng kì để tính win rate

            # Tính win rate từ historical data (dùng 36% từ test trước)
            estimated_win_rate = 0.36

            for period_index in range(min_periods, max_period):
                # Cập nhật Martingale tracking
                self.update_martingale_tracking()

                # Tính win rate gần đây (10 kì gần nhất) - chỉ khi đã có ít nhất 5 kì dữ liệu
                recent_periods = max(5, min(period_index - min_periods, 15))
                if recent_periods >= 5 and hasattr(self, '_period_results') and len(self._period_results) >= 5:
                    recent_wins = sum(1 for i in range(max(0, period_index - recent_periods), period_index)
                                    if i in self._period_results and self._period_results[i] > 0)
                    recent_win_rate = recent_wins / recent_periods
                else:
                    recent_win_rate = estimated_win_rate  # Sử dụng estimated win rate khi chưa có đủ dữ liệu

                # Kiểm tra có nên skip không
                if self.should_skip_prediction(recent_win_rate, consecutive_losses):
                    print(f"   ⏸️ KÌ {period_index + 1} - SKIP (dự đoán khó)")
                    continue

                # Kiểm tra điều kiện dừng
                if daily_profit >= self.target_profit:
                    print(f"   🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                    break

                if current_capital <= self.daily_capital * self.stop_loss_ratio:
                    print(f"   🛑 STOP LOSS! Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%) - DỪNG CHƠI")
                    break

                result = self.predict_period(day_draws, period_index)
                if result:
                    # Detect big win (lợi nhuận kì trước > 100k)
                    recent_big_win = last_period_profit > 100_000

                    # Tính giá vé động với logic mới
                    ticket_price = self.calculate_dynamic_ticket_price(consecutive_losses, consecutive_wins, daily_profit, recent_big_win)

                    # Tính số vé cần mua
                    tickets_to_buy = self.calculate_bet_size(current_capital, consecutive_losses, estimated_win_rate, ticket_price, consecutive_wins, daily_profit)

                    # CHIẾN THUẬT MỚI: Nếu không đủ vé, tăng giá vé thay vì tăng số vé
                    min_tickets_required = 5  # Tối thiểu cần 5 vé
                    if tickets_to_buy < min_tickets_required and tickets_to_buy > 0:
                        # Tính lại giá vé để có đủ min_tickets_required
                        available_budget = current_capital * 0.1  # Dùng tối đa 10% vốn
                        new_ticket_price = available_budget / min_tickets_required

                        if new_ticket_price >= self.min_ticket_price:
                            ticket_price = min(new_ticket_price, self.max_ticket_price * 3)  # Cho phép vượt max
                            tickets_to_buy = min_tickets_required
                            print(f"      💰 Điều chỉnh: {tickets_to_buy} vé @ {ticket_price:,.0f} VNĐ/vé (tăng giá thay vì giảm vé)")

                    if tickets_to_buy == 0:
                        print(f"   💸 Không đủ vốn để mua vé - DỪNG CHƠI")
                        break

                    # Tính số vé thắng - CHỈ TÍNH TRÊN CÁC VÉ THỰC TẾ MUA
                    tickets_to_display = min(tickets_to_buy, len(result['selected_combos']))
                    actual_tickets_bought = result['selected_combos'][:tickets_to_display]

                    winning_tickets = 0
                    winning_details = []
                    actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]

                    for i, combo in enumerate(actual_tickets_bought, 1):
                        missing_count = sum(1 for num in combo if num in actual_missing)
                        is_winning = missing_count >= 4
                        if is_winning:
                            winning_tickets += 1
                        winning_details.append({
                            'ticket_num': i,
                            'combo': combo,
                            'missing_count': missing_count,
                            'is_winning': is_winning
                        })

                    # Tính lợi nhuận dựa trên số vé thực tế mua
                    period_profit, period_cost, period_revenue = self.calculate_profit(tickets_to_display, winning_tickets, ticket_price)

                    # Cập nhật vốn và thống kê
                    current_capital += period_profit
                    daily_profit += period_profit
                    daily_cost += period_cost
                    daily_revenue += period_revenue
                    total_bets += 1

                    # Cập nhật consecutive wins/losses và lưu profit kì này
                    last_period_profit = period_profit

                    # Lưu kết quả kì này để tính win rate
                    self._period_results[period_index] = period_profit

                    if period_profit > 0:
                        consecutive_losses = 0
                        consecutive_wins += 1
                    else:
                        consecutive_losses += 1
                        consecutive_wins = 0

                    day_predictions += 1
                    day_winning += winning_tickets  # Sử dụng số vé thắng thực tế
                    day_total += tickets_to_display  # Sử dụng số vé thực tế mua
                    total_stats['total_predictions'] += 1
                    total_stats['winning_combos'] += winning_tickets  # Sử dụng số vé thắng thực tế
                    total_stats['total_combos'] += tickets_to_display  # Sử dụng số vé thực tế mua
                    total_stats['total_profit'] += period_profit
                    total_stats['total_cost'] += period_cost
                    total_stats['total_revenue'] += period_revenue

                    # Tính target progress
                    target_progress = (daily_profit / self.target_profit) * 100

                    # Log chi tiết với money management
                    self._print_period_details_with_money_v2(result, tickets_to_display, winning_tickets,
                                                        period_profit, current_capital, consecutive_losses,
                                                        daily_profit, target_progress, ticket_price, consecutive_wins,
                                                        winning_details, period_cost, period_revenue)
            
            if day_predictions > 0:
                total_stats['total_days'] += 1
                win_rate = (day_winning / day_total) * 100 if day_total > 0 else 0
                avg_winning_per_period = day_winning / day_predictions if day_predictions > 0 else 0

                # Lưu thống kê ngày
                total_stats['daily_stats'][test_date] = {
                    'predictions': day_predictions,
                    'total_combos': day_total,
                    'winning_combos': day_winning,
                    'win_rate': win_rate,
                    'daily_profit': daily_profit
                }

                print(f"   📊 Ngày {test_date}: {day_predictions} kì, {day_winning}/{day_total} vé thắng ({win_rate:.1f}%)")
                print(f"   📈 Trung bình: {avg_winning_per_period:.1f} vé thắng/kì")

                # Thêm daily summary
                print(f"\n   📊 THỐNG KÊ NGÀY {test_date}:")
                print(f"      • Tổng kì dự đoán: {day_predictions}")
                print(f"      • Tổng vé đánh: {day_total}")
                print(f"      • Tổng vé thắng: {day_winning}")
                print(f"      • Tỷ lệ thắng: {win_rate:.1f}%")
                print(f"      • Trung bình vé thắng/kì: {avg_winning_per_period:.1f}")
                print("   " + "="*50)
        
        # Hiển thị thống kê tổng hợp
        self._display_final_results(total_stats, start_date, end_date)

    def _print_period_details_with_money_v2(self, result, tickets_bought, winning_tickets,
                                        period_profit, current_capital, consecutive_losses,
                                        cumulative_profit, target_progress, ticket_price, consecutive_wins,
                                        winning_details=None, period_cost=0, period_revenue=0):
        """In chi tiết kết quả của một kì với money management và lợi nhuận tích lũy"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")

        # In Final 6 Strategy (rút gọn, weighted order)
        print(f"      🎯 Final 6: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])} (weighted order)")

        # Hiển thị các vé 4 số đã mua (chỉ hiển thị 5 vé đầu để không quá dài)
        if winning_details:
            print(f"      🎫 Vé đã mua ({len(winning_details)} vé):")
            for i, detail in enumerate(winning_details[:5], 1):  # Chỉ hiển thị 5 vé đầu
                status_icon = "✅" if detail['is_winning'] else "❌"
                sorted_combo = sorted(detail['combo'])
                combo_str = ' '.join([f'{num:2d}' for num in sorted_combo])
                print(f"         {i}: {combo_str} → {detail['missing_count']}/4 {status_icon}")
            if len(winning_details) > 5:
                print(f"         ... và {len(winning_details) - 5} vé khác")

        # In money management với dynamic pricing
        profit_icon = "🟢" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"

        # Hiển thị thông tin martingale/aggressive nếu có
        if consecutive_losses >= 5:
            print(f"      🔥 MARTINGALE 100%: Thua {consecutive_losses} kì → Tăng SỐ VÉ x2 + GIÁ VÉ x2 (3 kì)")
        elif consecutive_losses >= 3:
            print(f"      📈 MARTINGALE 50%: Thua {consecutive_losses} kì → Tăng SỐ VÉ x1.5 + GIÁ VÉ x1.5 (2 kì)")
        elif consecutive_wins >= 6:
            print(f"      🚀 AGGRESSIVE 300%: Thắng {consecutive_wins} kì → Tăng SỐ VÉ x3 + GIÁ VÉ x3")
        elif consecutive_wins >= 3:
            print(f"      🔥 AGGRESSIVE 200%: Thắng {consecutive_wins} kì → Tăng SỐ VÉ x2 + GIÁ VÉ x2")
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            print(f"      💎 PROFIT-BASED: Thắng {consecutive_wins} kì, lời {cumulative_profit:,} VNĐ → Dùng 50% lời để tăng cược")

        if period_cost > 0 and period_revenue > 0:
            print(f"      💰 Chi phí: {tickets_bought} vé × {ticket_price:,} = {period_cost:,} VNĐ")
            print(f"      💰 Thu nhập: {winning_tickets} vé × {ticket_price:,} × {self.win_multiplier} = {period_revenue:,} VNĐ")
            print(f"      💰 Lợi nhuận: {profit_icon} {period_profit:+,} VNĐ")
        else:
            print(f"      💰 Mua {tickets_bought} vé @ {ticket_price:,} VNĐ/vé → Thắng {winning_tickets} vé → {profit_icon} {period_profit:+,} VNĐ")
        print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")

        # Hiển thị thông tin giá vé động với logic mới
        price_status = ""
        if ticket_price > self.base_ticket_price:
            price_increase = ((ticket_price - self.base_ticket_price) / self.base_ticket_price) * 100
            if price_increase >= 200:
                price_status = f"🔥 Tăng giá {price_increase:.0f}% (MARTINGALE 300%)"
            elif price_increase >= 100:
                price_status = f"📈 Tăng giá {price_increase:.0f}% (MARTINGALE 200%)"
            elif price_increase >= 40:
                price_status = f"📈 Tăng giá {price_increase:.0f}% (thắng đậm - aggressive)"
            elif price_increase >= 20:
                if consecutive_wins >= 2:
                    price_status = f"📈 Tăng giá {price_increase:.0f}% (đang lãi - aggressive)"
                else:
                    price_status = f"� Tăng giá {price_increase:.0f}% (thua 3 kì liên tiếp)"
            else:
                price_status = f"📈 Tăng giá {price_increase:.1f}%"
        else:
            price_status = "📊 Giá cơ bản"
        print(f"      🎫 {price_status}")

        # Thêm logs lợi nhuận tích lũy
        cumulative_icon = "🟢" if cumulative_profit > 0 else "🔴" if cumulative_profit < 0 else "🟡"
        print(f"      📈 Lợi nhuận tích lũy: {cumulative_icon} {cumulative_profit:+,} VNĐ")
        print(f"      🎯 Tiến độ mục tiêu: {target_progress:.1f}% ({cumulative_profit:,}/{self.target_profit:,} VNĐ)")

        # Hiển thị còn thiếu bao nhiêu để đạt mục tiêu
        remaining = self.target_profit - cumulative_profit
        if remaining > 0:
            print(f"      📊 Còn thiếu: {remaining:,} VNĐ để đạt mục tiêu")
        else:
            print(f"      🎉 ĐÃ VƯỢT MỤC TIÊU: +{abs(remaining):,} VNĐ")

        if consecutive_losses > 0:
            print(f"      ⚠️ Thua liên tiếp: {consecutive_losses} lần")

        # Prediction stats (rút gọn)
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"      📊 Prediction: {result['winning_combos']}/{result['total_combos']} ({win_rate:.1f}%)")

    def _print_period_details_with_money(self, result, tickets_bought, winning_tickets,
                                        period_profit, current_capital, consecutive_losses):
        """In chi tiết kết quả của một kì với money management"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")

        # In Final 6 Strategy (rút gọn)
        print(f"      🎯 Final 6: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])}")

        # In money management
        profit_status = "�" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"
        print(f"      💰 Mua {tickets_bought} vé → Thắng {winning_tickets} vé → {profit_status} {period_profit:+,} VNĐ")
        print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")

        if consecutive_losses > 0:
            print(f"      ⚠️ Thua liên tiếp: {consecutive_losses} lần")

        # Prediction stats (rút gọn)
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"      📊 Prediction: {result['winning_combos']}/{result['total_combos']} ({win_rate:.1f}%)")

    def _print_period_details(self, result):
        """In chi tiết kết quả của một kì (legacy method)"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")
        print(f"      📊 Kết quả thực tế: {result['actual_results']}")

        # In Final 6 Strategy
        print(f"      🎯 Final 6 Strategy:")
        print(f"         Numbers: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])}")
        print(f"         Source:  Weighted combination from LSTM + Seasonal + Excluded")
        print(f"         Legend:  Top 6 numbers by weight score")

        # In thống kê bộ 4 số
        print(f"      🎲 Random 4-number combos:")
        print(f"         Vé thắng: {result['winning_combos']}/{result['total_combos']} vé")
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"         Tỷ lệ thắng: {win_rate:.1f}%")

    def _display_final_results(self, total_stats, start_date, end_date):
        """Hiển thị kết quả tổng hợp"""
        print(f"\n🎯 THỐNG KÊ TỔNG HỢP ({start_date} → {end_date})")
        print("="*60)
        print(f"Tổng: {total_stats['total_days']} ngày, {total_stats['total_predictions']} predictions")

        # Hiển thị thống kê từng ngày
        if total_stats['daily_stats']:
            print(f"\n📊 THỐNG KÊ TỪNG NGÀY:")
            print("   Ngày        | Kì  | Vé thắng/Tổng vé | Tỷ lệ % | Lợi nhuận")
            print("   " + "-"*62)

            for date, stats in total_stats['daily_stats'].items():
                profit_display = f"{stats['daily_profit']:+,.0f}" if stats['daily_profit'] != 0 else "0"
                print(f"   {date} | {stats['predictions']:2d}  | "
                      f"{stats['winning_combos']:3d}/{stats['total_combos']:3d}        | "
                      f"{stats['win_rate']:5.1f}% | {profit_display:>10}")

        print(f"\n💰 MONEY MANAGEMENT RESULTS:")
        if total_stats['total_cost'] > 0:
            total_roi = (total_stats['total_profit'] / total_stats['total_cost']) * 100
            print(f"   Tổng chi phí: {total_stats['total_cost']:,} VNĐ")
            print(f"   Tổng doanh thu: {total_stats['total_revenue']:,} VNĐ")
            print(f"   Tổng lợi nhuận: {total_stats['total_profit']:+,} VNĐ")
            print(f"   ROI: {total_roi:+.1f}%")
            print(f"   Lợi nhuận/ngày: {total_stats['total_profit']/total_stats['total_days']:+,.0f} VNĐ")

            # Đánh giá mục tiêu
            target_achievement = (total_stats['total_profit']/total_stats['total_days']) / self.target_profit * 100
            if target_achievement >= 100:
                print(f"   🎯 ĐẠT MỤC TIÊU: {target_achievement:.1f}% mục tiêu ({self.target_profit:,} VNĐ/ngày)")
            else:
                print(f"   ⚠️ CHƯA ĐẠT MỤC TIÊU: {target_achievement:.1f}% mục tiêu ({self.target_profit:,} VNĐ/ngày)")

        print(f"\n🎲 Trading Performance Overall Stats:")
        if total_stats['total_combos'] > 0:
            win_rate = (total_stats['winning_combos'] / total_stats['total_combos']) * 100
            print(f"   Tổng vé thắng: {total_stats['winning_combos']}")
            print(f"   Tổng vé đã mua: {total_stats['total_combos']}")
            print(f"   Tỷ lệ thắng vé: {win_rate:.1f}%")
            print(f"   Trung bình vé thắng/kì: {total_stats['winning_combos']/total_stats['total_predictions']:.1f}")
            print(f"   Trung bình vé mua/kì: {total_stats['total_combos']/total_stats['total_predictions']:.1f}")

            # Thống kê performance
            if total_stats['daily_stats']:
                daily_rates = [stats['win_rate'] for stats in total_stats['daily_stats'].values()]
                avg_daily_rate = sum(daily_rates) / len(daily_rates)
                max_daily_rate = max(daily_rates)
                min_daily_rate = min(daily_rates)

                print(f"\n📈 Performance Analysis:")
                print(f"   Tỷ lệ thắng trung bình/ngày: {avg_daily_rate:.1f}%")
                print(f"   Tỷ lệ thắng cao nhất: {max_daily_rate:.1f}%")
                print(f"   Tỷ lệ thắng thấp nhất: {min_daily_rate:.1f}%")
        else:
            print(f"   No data available")

        print("="*60)

def test_range(start_date, end_date, min_periods=50):
    """Function để test từ command line"""
    predictor = FinalKenoPredictor()
    return predictor.test_date_range(start_date, end_date, min_periods)

if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3 or len(sys.argv) > 4:
        print("Usage: python final_keno_predictor.py START_DATE END_DATE [MIN_PERIODS]")
        print("Example: python final_keno_predictor.py 2025-04-01 2025-04-30")
        print("Example: python final_keno_predictor.py 2025-04-01 2025-04-30 30")
        sys.exit(1)

    start_date = sys.argv[1]
    end_date = sys.argv[2]
    min_periods = int(sys.argv[3]) if len(sys.argv) == 4 else 50

    test_range(start_date, end_date, min_periods)
