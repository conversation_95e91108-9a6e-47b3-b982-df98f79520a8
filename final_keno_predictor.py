#!/usr/bin/env python3
"""
Final Keno Predictor - Simplified Version
Chỉ log ra 7 số chính và tạo ngẫu nhiên 10-20 cặp 4 số
"""

import mysql.connector
import random
from datetime import datetime, timedelta
from itertools import combinations
from collections import Counter

# KENO PERIOD CONSTANTS
KENO_START_TIME = "06:00:00"  # Kì đầu tiên
KENO_END_TIME = "21:44:00"    # Kì cuối cùng
KENO_INTERVAL_MINUTES = 8     # Mỗi kì cách nhau 8 phút
KENO_FIRST_PERIOD = 1         # Period đầu tiên
KENO_LAST_PERIOD = 119        # Period cuối cùng

def connect_db():
    """Kết nối database"""
    return mysql.connector.connect(
        host='localhost',
        user='root',
        port=3307,
        password='1',
        database='keno'
    )

def time_to_period(time_str):
    """Chuyển đổi time string thành period number

    Args:
        time_str: Time string format "HH:MM:SS" (e.g., "06:00:00")

    Returns:
        int: Period number (1-119) hoặc None nếu không hợp lệ

    Examples:
        "06:00:00" -> 1
        "06:08:00" -> 2
        "21:44:00" -> 119
    """
    try:
        # Parse time
        time_obj = datetime.strptime(time_str, "%H:%M:%S").time()
        start_time = datetime.strptime(KENO_START_TIME, "%H:%M:%S").time()

        # Tính số phút từ start time
        start_minutes = start_time.hour * 60 + start_time.minute
        current_minutes = time_obj.hour * 60 + time_obj.minute

        # Tính period
        minutes_diff = current_minutes - start_minutes
        if minutes_diff < 0:
            return None  # Trước giờ bắt đầu

        period = (minutes_diff // KENO_INTERVAL_MINUTES) + 1

        # Kiểm tra trong khoảng hợp lệ
        if period < KENO_FIRST_PERIOD or period > KENO_LAST_PERIOD:
            return None

        return period
    except:
        return None

def period_to_time(period):
    """Chuyển đổi period number thành time string

    Args:
        period: Period number (1-119)

    Returns:
        str: Time string format "HH:MM:SS" hoặc None nếu không hợp lệ

    Examples:
        1 -> "06:00:00"
        2 -> "06:08:00"
        119 -> "21:44:00"
    """
    try:
        if period < KENO_FIRST_PERIOD or period > KENO_LAST_PERIOD:
            return None

        # Tính số phút từ start time
        minutes_from_start = (period - 1) * KENO_INTERVAL_MINUTES

        # Parse start time
        start_time = datetime.strptime(KENO_START_TIME, "%H:%M:%S")

        # Thêm minutes
        result_time = start_time + timedelta(minutes=minutes_from_start)

        return result_time.strftime("%H:%M:%S")
    except:
        return None

def validate_period_time(period, time_str):
    """Kiểm tra period và time có khớp nhau không

    Args:
        period: Period number
        time_str: Time string

    Returns:
        bool: True nếu khớp, False nếu không khớp
    """
    expected_time = period_to_time(period)
    expected_period = time_to_period(time_str)

    return expected_time == time_str and expected_period == period

class FinalKenoPredictor:
    def __init__(self):
        # Money Management Settings
        self.daily_capital = 3_000_000  # 3 triệu VNĐ/ngày
        self.target_profit = 50_000_000  # 2 triệu VNĐ/ngày

        # Dynamic Pricing Settings
        self.base_ticket_price = 15_000     # Giá vé cơ bản 10k VNĐ
        self.min_ticket_price = 15_000      # Giá vé tối thiểu
        self.max_ticket_price = 150_000      # Giá vé tối đa
        self.win_multiplier = 3.2           # Tỷ lệ thắng 3.2x

        # Betting Settings
        self.base_tickets = 10               # Số vé cơ bản mỗi lần
        self.min_tickets = 10                # Tối thiểu 5 vé/lần
        self.max_tickets = 20               # Tối đa 10 vé/lần (giảm để tập trung volume vào giá vé)
        self.stop_loss_ratio = 0.1          # Dừng khi còn 20% vốn
        self.martingale_trigger = 5         # Bắt đầu martingale từ 3 ván thua

        # Martingale Tracking Settings
        self.martingale_periods_remaining = 0  # Số kì còn lại áp dụng Martingale
        self.martingale_type = ""  # Loại Martingale đang áp dụng

        # Skip Strategy Settings
        self.skip_periods_remaining = 0  # Số kì còn lại cần skip
        self.prediction_difficulty_threshold = 0.10  # Giảm từ 15% → 10% để ít skip hơn
        self.recent_results = []  # Lưu kết quả các kỳ gần nhất để tính miss rate

        # Loại bỏ Win Rate Strategy - quay về hệ thống đơn giản

        # Phase 2: Adaptive Learning Settings
        self.method_performance = {
            'lstm': [],
            'excluded': [],
            'pattern': [],
            'hot': [],
            'cold': []
        }
        self.method_weights = {
            'lstm': 0.30,
            'excluded': 0.25,
            'pattern': 0.20,
            'hot': 0.15,
            'cold': 0.10
        }
        self.performance_window = 20  # Chỉ giữ 20 kết quả gần nhất
        self.confidence_scores = []  # Lưu confidence score từng kì

        print("✅ Final Keno Predictor with Dynamic Pricing + Phase 2 Adaptive Learning initialized")
        print(f"💰 Vốn/ngày: {self.daily_capital:,} VNĐ")
        print(f"🎯 Mục tiêu: {self.target_profit:,} VNĐ/ngày")
        print(f"🎫 Giá vé: {self.min_ticket_price:,} - {self.max_ticket_price:,} VNĐ (dynamic)")
        print(f"🏆 Tỷ lệ thắng: {self.win_multiplier}x (chưa trừ chi phí)")
        print(f"📊 Số vé cơ bản: {self.base_tickets} vé/lần")

    def get_day_draws(self, date):
        """Lấy tất cả kì của một ngày"""
        try:
            print(f"🔍 Trying to load data for date: {date}")
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            # Debug: Kiểm tra dữ liệu có sẵn
            cursor.execute("SELECT MIN(date), MAX(date), COUNT(*) FROM histories_keno")
            result = cursor.fetchone()
            min_date, max_date, total = result['MIN(date)'], result['MAX(date)'], result['COUNT(*)']
            print(f"📊 Database range: {min_date} to {max_date} ({total} records)")

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (date,))
            rows = cursor.fetchall()
            print(f"📅 Found {len(rows)} records for {date}")

            cursor.close()
            conn.close()

            if not rows:
                print(f"⚠️ No data found for {date}, using sample data")
                return self._generate_sample_data()

            # Chuyển đổi results từ string thành list
            for row in rows:
                if isinstance(row['results'], str):
                    # Xử lý string results
                    row['results'] = [int(n.strip()) for n in row['results'].split(',') if n.strip()]
                elif isinstance(row['results'], list):
                    # Đã là list, không cần xử lý
                    pass
                else:
                    # Fallback cho các trường hợp khác
                    print(f"⚠️ Unexpected results format: {type(row['results'])}")
                    row['results'] = []

            print(f"✅ Successfully loaded {len(rows)} periods for {date}")
            return rows

        except Exception as e:
            print(f"❌ Error loading day draws: {e}")
            print(f"🔄 Using sample data instead")
            # Fallback: Tạo dữ liệu mẫu để demo
            return self._generate_sample_data()

    def _generate_sample_data(self):
        """Tạo dữ liệu mẫu để demo"""
        sample_data = []
        for i in range(80):  # 80 kì
            # Tạo 20 số ngẫu nhiên từ 1-80
            results = sorted(random.sample(range(1, 81), 20))
            sample_data.append({
                'time': f"08:{i:02d}:00",
                'results': results,
                'period': i + 1
            })
        return sample_data

    def calculate_dynamic_ticket_price(self, consecutive_losses, consecutive_wins, cumulative_profit, recent_big_win=False):
        """Tính giá vé động dựa trên tình hình thắng/thua với chiến thuật mới + profit-based betting"""
        base_price = self.base_ticket_price
        price = base_price

        # CHIẾN THUẬT MỚI: Martingale cải tiến - tăng giá vé khi thua liên tiếp
        if consecutive_losses >= 5:
            # Thua 5-6 kì liên tiếp → tăng giá vé 100% (x2.0) trong 3 kì
            price = base_price * 2.0
            if self.martingale_periods_remaining <= 0:
                self.martingale_periods_remaining = 3  # Áp dụng trong 3 kì
                self.martingale_type = "100%"
            print(f"      🔥 MARTINGALE 100%: Thua {consecutive_losses} kì → Tăng giá vé x2.0 (còn {self.martingale_periods_remaining} kì)")
        elif consecutive_losses >= 3:
            # Thua 3 kì liên tiếp → tăng giá vé 50% (x1.5) trong 2 kì
            price = base_price * 1.5
            if self.martingale_periods_remaining <= 0:
                self.martingale_periods_remaining = 2  # Áp dụng trong 2 kì
                self.martingale_type = "50%"
            print(f"      📈 MARTINGALE 50%: Thua {consecutive_losses} kì → Tăng giá vé x1.5 (còn {self.martingale_periods_remaining} kì)")

        # CHIẾN THUẬT MỚI: Tăng giá vé khi thắng liên tiếp (aggressive betting)
        elif consecutive_wins >= 6:
            # Thắng 6 kì liên tiếp → tăng giá vé 300%
            price = base_price * 3.0
        elif consecutive_wins >= 3:
            # Thắng 3 kì liên tiếp → tăng giá vé 200%
            price = base_price * 2.0
        elif recent_big_win:
            # Thắng đậm → tăng 40% để maximize profit
            price = base_price * 1.4

        # CHIẾN THUẬT MỚI: Tăng cược dựa trên lợi nhuận tích lũy khi thắng liên tiếp
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            # Thắng liên tiếp 2-3 ván và đang có lời → dùng 50% lợi nhuận để tăng giá vé
            profit_boost = cumulative_profit * 0.5  # 50% lợi nhuận

            # Tính giá vé mới với profit boost (tối đa tăng 200% từ lợi nhuận)
            profit_multiplier = min(profit_boost / base_price, 2.0)  # Tối đa x3 (base + 200%)
            price = base_price * (1 + profit_multiplier)

            print(f"      💰 PROFIT-BASED BETTING: Thắng {consecutive_wins} kì liên tiếp, lời {cumulative_profit:,} VNĐ")
            print(f"      💰 Dùng 50% lời ({profit_boost:,} VNĐ) để tăng giá vé từ {base_price:,} → {price:,.0f} VNĐ")

        # Giữ giá cơ bản trong các trường hợp khác
        else:
            price = base_price

        # Cho phép vượt max_ticket_price trong trường hợp martingale, aggressive betting hoặc profit-based
        max_allowed_price = self.max_ticket_price * 3 if (consecutive_losses >= 3 or consecutive_wins >= 2) else self.max_ticket_price
        return max(self.min_ticket_price, min(price, max_allowed_price))

    def calculate_bet_size(self, current_capital, consecutive_losses, win_rate, ticket_price, consecutive_wins=0, cumulative_profit=0):
        """Tính số vé cần mua với chiến thuật mới + profit-based betting"""
        # Bắt đầu với base tickets
        base_tickets = self.base_tickets

        # Điều chỉnh dựa trên win rate
        if win_rate >= 0.40:  # Win rate rất cao
            base_tickets = self.base_tickets
        elif win_rate >= 0.35:  # Win rate cao
            base_tickets = max(self.base_tickets - 2, self.min_tickets)
        elif win_rate >= 0.30:  # Win rate trung bình
            base_tickets = max(self.base_tickets - 3, self.min_tickets)
        else:  # Win rate thấp
            base_tickets = self.min_tickets

        # CHIẾN THUẬT MỚI: Martingale cải tiến - tăng số vé khi thua liên tiếp
        if consecutive_losses >= 5:
            # Thua 5-6 kì liên tiếp → tăng số vé 100% (x2.0)
            base_tickets = int(base_tickets * 2.0)
            print(f"      🎫 MARTINGALE 100%: Thua {consecutive_losses} kì → Tăng số vé x2.0")
        elif consecutive_losses >= 3:
            # Thua 3 kì liên tiếp → tăng số vé 50% (x1.5)
            base_tickets = int(base_tickets * 1.5)
            print(f"      🎫 MARTINGALE 50%: Thua {consecutive_losses} kì → Tăng số vé x1.5")

        # CHIẾN THUẬT MỚI: Tăng cược khi thắng liên tiếp (aggressive betting)
        elif consecutive_wins >= 6:
            # Thắng 6 kì liên tiếp → tăng 300% (aggressive betting)
            base_tickets = int(base_tickets * 3.0)
        elif consecutive_wins >= 3:
            # Thắng 3 kì liên tiếp → tăng 200% (aggressive betting)
            base_tickets = int(base_tickets * 2.0)

        # CHIẾN THUẬT MỚI: Tăng số vé dựa trên lợi nhuận khi thắng liên tiếp 2+ ván
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            # Tính số vé bổ sung từ 50% lợi nhuận
            profit_boost = cumulative_profit * 0.5  # 50% lợi nhuận
            additional_tickets = int(profit_boost // ticket_price)  # Số vé có thể mua thêm

            # Tăng số vé nhưng không quá 50% base tickets
            max_additional = int(base_tickets * 0.5)
            additional_tickets = min(additional_tickets, max_additional)

            if additional_tickets > 0:
                base_tickets += additional_tickets
                print(f"      🎫 PROFIT-BASED TICKETS: Thêm {additional_tickets} vé từ lợi nhuận ({profit_boost:,} VNĐ)")

        # Giới hạn theo vốn còn lại và cho phép vượt max_tickets trong trường hợp martingale hoặc profit-based
        max_affordable = int(current_capital // ticket_price)
        final_tickets = int(min(base_tickets, max_affordable, self.max_tickets * 2))  # Cho phép vượt max_tickets

        return int(max(final_tickets, self.min_tickets)) if final_tickets >= self.min_tickets else 0

    def should_skip_prediction(self, recent_win_rate, consecutive_losses):
        """Kiểm tra có nên skip dự đoán không dựa trên tỷ lệ trượt cao"""
        # Nếu đang trong thời gian skip
        if self.skip_periods_remaining > 0:
            self.skip_periods_remaining -= 1
            print(f"      ⏸️ SKIP: Còn {self.skip_periods_remaining} kì cần skip")
            return True

        # Kiểm tra tỷ lệ trượt trong 3-5 kỳ gần nhất
        if len(self.recent_results) >= 3:
            # Lấy 3-5 kỳ gần nhất (tối đa 5 kỳ)
            recent_periods = min(5, len(self.recent_results))
            recent_data = self.recent_results[-recent_periods:]

            # Tính tỷ lệ trượt trung bình
            total_miss_rate = 0
            valid_periods = 0

            for result in recent_data:
                if 'miss_rate' in result and result['miss_rate'] is not None:
                    total_miss_rate += result['miss_rate']
                    valid_periods += 1

            if valid_periods >= 3:  # Cần ít nhất 3 kỳ để đánh giá
                avg_miss_rate = total_miss_rate / valid_periods

                # Nếu tỷ lệ trượt >= 80% thì skip 1 kỳ (điều chỉnh từ 70% → 80%)
                if avg_miss_rate >= 0.80:
                    skip_periods = 1  # Chỉ skip 1 kỳ để tránh skip quá nhiều

                    self.skip_periods_remaining = skip_periods - 1  # -1 vì kỳ hiện tại đã skip
                    print(f"      ⏸️ SKIP STRATEGY: Tỷ lệ trượt cao {avg_miss_rate:.1%} trong {valid_periods} kỳ gần nhất → Skip {skip_periods} kỳ")

                    # Decay miss rate để tránh skip vô hạn
                    for result in recent_data:
                        if 'miss_rate' in result:
                            result['miss_rate'] *= 0.8  # Giảm 20% miss rate

                    return True

        return False

    def evaluate_win_rate_strategy(self, cumulative_profit, recent_win_rate, consecutive_losses):
        """Đánh giá và kích hoạt chiến lược dựa trên tỷ lệ thắng 10-28%"""

        # Xác định cấp độ khó dựa trên tỷ lệ thắng
        current_level = "NORMAL"
        for level, config in self.difficulty_levels.items():
            min_rate, max_rate = config["win_rate_range"]
            if min_rate <= recent_win_rate <= max_rate:
                current_level = level
                break

        # Nếu tỷ lệ thắng > 28% thì không áp dụng strategy đặc biệt
        if recent_win_rate > 0.28:
            if self.win_rate_strategy_active:
                self.win_rate_strategy_active = False
                self.current_difficulty_level = "NORMAL"
                print(f"      ✅ WIN RATE STRATEGY kết thúc: Tỷ lệ thắng {recent_win_rate:.1%} > 28%")
            return "NORMAL"

        # Lấy config cho level hiện tại
        if current_level == "NORMAL":
            return "NORMAL"

        level_config = self.difficulty_levels[current_level]

        # Kiểm tra stop loss cho level này
        if cumulative_profit <= level_config["stop_loss"]:
            print(f"      🛑 STOP LOSS ({current_level}): Lỗ {cumulative_profit:,} VNĐ >= {level_config['stop_loss']:,} VNĐ → DỪNG ĐÁNH")
            return "STOP"

        # Kiểm tra điều kiện kích hoạt strategy
        should_activate = (
            cumulative_profit <= level_config["min_loss_to_activate"] and
            consecutive_losses >= 2  # Thua ít nhất 2 kỳ liên tiếp
        )

        # Nếu đang trong strategy mode
        if self.win_rate_strategy_active:
            if self.strategy_periods_remaining > 0:
                self.strategy_periods_remaining -= 1
                print(f"      🎯 {current_level} STRATEGY: Còn {self.strategy_periods_remaining} kỳ")
                return f"CONTINUE_{level_config['action']}"
            else:
                # Kết thúc strategy mode
                self.win_rate_strategy_active = False
                self.current_difficulty_level = "NORMAL"
                print(f"      ✅ {current_level} STRATEGY kết thúc")
                return "END_STRATEGY"

        # Kích hoạt strategy nếu đủ điều kiện
        if should_activate:
            self.win_rate_strategy_active = True
            self.current_difficulty_level = current_level

            # Tính số kỳ strategy dựa trên level
            if current_level == "EXTREME":
                strategy_periods = 999  # Dừng đánh hoàn toàn
            elif current_level == "VERY_HARD":
                strategy_periods = 5
            elif current_level == "HARD":
                strategy_periods = 4
            elif current_level == "MODERATE":
                strategy_periods = level_config.get("recovery_periods", 3)

            self.strategy_periods_remaining = strategy_periods - 1

            print(f"      🚨 KÍCH HOẠT {current_level} STRATEGY:")
            print(f"         � Tỷ lệ thắng: {recent_win_rate:.1%} ({level_config['description']})")
            print(f"         � Lỗ tích lũy: {cumulative_profit:,} VNĐ")
            print(f"         ⚠️ Thua liên tiếp: {consecutive_losses} kỳ")
            print(f"         🎯 Hành động: {level_config['action']}")
            print(f"         ⏱️ Thời gian: {strategy_periods} kỳ")

            return f"START_{level_config['action']}"

        return "NORMAL"

    def calculate_strategy_adjustments(self, base_tickets, base_price):
        """Tính toán điều chỉnh số vé và giá vé dựa trên win rate strategy"""
        if not self.win_rate_strategy_active:
            return base_tickets, base_price

        level_config = self.difficulty_levels[self.current_difficulty_level]
        action = level_config["action"]

        if action == "STOP_BETTING":
            return 0, base_price  # Dừng đánh hoàn toàn

        elif action == "MINIMAL_BETTING":
            # CHỈ ĐIỀU CHỈNH GIÁ VÉ, GIỮ NGUYÊN SỐ VÉ
            adjusted_price = base_price * level_config["price_multiplier"]

            print(f"      🔻 MINIMAL BETTING ({self.current_difficulty_level}):")
            print(f"         🎫 Số vé: {base_tickets} (giữ nguyên)")
            print(f"         💰 Giá vé: {base_price:,.0f} → {adjusted_price:,.0f} VNĐ (x{level_config['price_multiplier']})")

            return base_tickets, adjusted_price

        elif action == "CONSERVATIVE_BETTING":
            # CHỈ ĐIỀU CHỈNH GIÁ VÉ, GIỮ NGUYÊN SỐ VÉ
            adjusted_price = base_price * level_config["price_multiplier"]

            print(f"      ⚖️ CONSERVATIVE BETTING ({self.current_difficulty_level}):")
            print(f"         🎫 Số vé: {base_tickets} (giữ nguyên)")
            print(f"         💰 Giá vé: {base_price:,.0f} → {adjusted_price:,.0f} VNĐ (x{level_config['price_multiplier']})")

            return base_tickets, adjusted_price

        elif action == "RECOVERY_BETTING":

            # CHỈ ĐIỀU CHỈNH GIÁ VÉ, GIỮ NGUYÊN SỐ VÉ
            adjusted_price = base_price * level_config["price_multiplier"]

            print(f"      � RECOVERY BETTING ({self.current_difficulty_level}):")
            print(f"         � Số vé: {base_tickets} (giữ nguyên)")
            print(f"         � Giá vé: {base_price:,.0f} → {adjusted_price:,.0f} VNĐ (x{level_config['price_multiplier']})")
            print(f"         🎯 Mục tiêu: Bù lỗ trong {self.strategy_periods_remaining + 1} kỳ")

            return base_tickets, adjusted_price

        return base_tickets, base_price

    def update_martingale_tracking(self):
        """Cập nhật tracking cho Martingale"""
        if self.martingale_periods_remaining > 0:
            self.martingale_periods_remaining -= 1
            if self.martingale_periods_remaining <= 0:
                print(f"      ✅ MARTINGALE {self.martingale_type} kết thúc")
                self.martingale_type = ""

    def calculate_profit(self, tickets_bought, winning_tickets, ticket_price):
        """Tính lợi nhuận từ một lần chơi với dynamic pricing"""
        cost = tickets_bought * ticket_price
        revenue = winning_tickets * ticket_price * self.win_multiplier
        profit = revenue - cost
        return profit, cost, revenue

    def get_lstm_predictions(self, day_results, num_predictions=10):
        """Dự đoán LSTM các số có khả năng TRƯỢT cao - dựa trên frequency thấp"""
        try:
            # Chỉ lấy data các kì trong ngày hiện tại
            all_numbers = []
            for result in day_results:
                all_numbers.extend(result['results'])

            if not all_numbers:
                print(f"      ⚠️ LSTM: Không có dữ liệu trong ngày, dùng fallback 1-{num_predictions}")
                return list(range(1, num_predictions + 1))

            # Kiểm tra số kì có đủ >= num_predictions không
            if len(day_results) < num_predictions:
                print(f"      ⚠️ LSTM: Chỉ có {len(day_results)} kì, cần tối thiểu {num_predictions} kì")
                return list(range(1, num_predictions + 1))

            # ✅ SỬA: Đếm tần suất và lấy số có tần suất THẤP NHẤT (khả năng trượt cao)
            frequency = Counter(all_numbers)
            all_numbers_set = set(range(1, 81))

            # Tính frequency cho tất cả số từ 1-80
            number_frequencies = {num: frequency.get(num, 0) for num in all_numbers_set}

            # Sắp xếp theo tần suất TĂNG DẦN (số ít xuất hiện nhất trước)
            least_common = sorted(number_frequencies.items(), key=lambda x: x[1])
            predictions = [num for num, _ in least_common[:num_predictions]]

            print(f"      📊 LSTM top {len(predictions)} số TRƯỢT (từ {len(day_results)} kì trong ngày): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ LSTM error: {e}, dùng fallback 1-{num_predictions}")
            return list(range(1, num_predictions + 1))



    def get_excluded_numbers(self, day_results):
        """Lấy các số bị loại trừ - sử dụng logic gap analysis"""
        try:
            if not day_results:
                print(f"      ⚠️ Excluded: Không có dữ liệu")
                return set()

            # Lấy 20 kết quả gần nhất
            recent_results = day_results[-20:]
            all_recent_numbers = []
            for result in recent_results:
                all_recent_numbers.extend(result['results'])

            # Tìm các số xuất hiện ít nhất (gap analysis)
            frequency = Counter(all_recent_numbers)
            all_numbers = set(range(1, 81))

            # Lấy các số có tần suất thấp nhất (dưới 25% percentile)
            freq_values = list(frequency.values())
            if freq_values:
                threshold = sorted(freq_values)[len(freq_values) // 4] if len(freq_values) > 4 else 1
                excluded = {num for num in all_numbers if frequency.get(num, 0) <= threshold}
            else:
                excluded = set(range(1, 21))  # Fallback: số 1-20

            excluded_list = sorted(list(excluded))
            print(f"      📊 Excluded ({len(excluded_list)}): {excluded_list[:5]}... (gap analysis)")
            return excluded
        except Exception as e:
            print(f"      ❌ Excluded error: {e}")
            return set(range(21, 41))  # Fallback khác

    def get_hot_numbers(self, day_results, num_predictions=10, lookback=30):
        """Lấy các số nóng - xuất hiện nhiều trong thời gian gần đây"""
        try:
            # Lấy dữ liệu gần đây
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if not recent_results:
                return list(range(1, num_predictions + 1))

            # Đếm tần suất xuất hiện
            hot_numbers = []
            for result in recent_results:
                hot_numbers.extend(result['results'])

            frequency = Counter(hot_numbers)
            most_common = frequency.most_common(num_predictions)
            predictions = [num for num, _ in most_common]

            print(f"      🔥 Hot Numbers top {len(predictions)} (từ {len(recent_results)} kì gần nhất): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ Hot Numbers error: {e}")
            return list(range(1, num_predictions + 1))

    def get_cold_numbers(self, day_results, num_predictions=10, lookback=50):
        """Lấy các số lạnh - ít xuất hiện trong thời gian gần đây"""
        try:
            # Lấy dữ liệu gần đây
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if not recent_results:
                return list(range(71, 71 + num_predictions))

            # Đếm tần suất xuất hiện
            cold_numbers = []
            for result in recent_results:
                cold_numbers.extend(result['results'])

            frequency = Counter(cold_numbers)

            # Tìm các số xuất hiện ít nhất
            all_numbers = set(range(1, 81))
            number_counts = {num: frequency.get(num, 0) for num in all_numbers}

            # Sắp xếp theo tần suất tăng dần (số lạnh nhất trước)
            sorted_cold = sorted(number_counts.items(), key=lambda x: x[1])
            predictions = [num for num, _ in sorted_cold[:num_predictions]]

            print(f"      ❄️ Cold Numbers top {len(predictions)} (từ {len(recent_results)} kì gần nhất): {predictions[:5]}...")
            return predictions
        except Exception as e:
            print(f"      ❌ Cold Numbers error: {e}")
            return list(range(71, 71 + num_predictions))

    def analyze_recent_patterns(self, day_results, lookback=20):
        """Phân tích patterns gần đây để điều chỉnh strategy"""
        try:
            recent_results = day_results[-lookback:] if len(day_results) > lookback else day_results

            if len(recent_results) < 5:
                return {}

            # Phân tích chu kỳ xuất hiện
            number_gaps = {}
            for num in range(1, 81):
                last_seen = -1
                gaps = []
                for i, result in enumerate(recent_results):
                    if num in result['results']:
                        if last_seen >= 0:
                            gaps.append(i - last_seen)
                        last_seen = i

                if gaps and len(gaps) >= 2:
                    avg_gap = sum(gaps) / len(gaps)
                    current_gap = len(recent_results) - last_seen - 1 if last_seen >= 0 else len(recent_results)

                    # Số có chu kỳ đều và sắp đến lượt (gap âm = sắp xuất hiện)
                    if avg_gap > 0:
                        gap_score = avg_gap - current_gap
                        number_gaps[num] = gap_score

            return number_gaps
        except Exception as e:
            print(f"      ❌ Pattern analysis error: {e}")
            return {}

    def get_pattern_predictions(self, day_results, num_predictions=10):
        """Dự đoán dựa trên pattern chu kỳ"""
        try:
            patterns = self.analyze_recent_patterns(day_results)

            if not patterns:
                return list(range(31, 31 + num_predictions))

            # Sắp xếp theo độ ưu tiên (số âm = sắp xuất hiện)
            sorted_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)
            predictions = [num for num, _ in sorted_patterns[:num_predictions]]

            print(f"      🔄 Pattern Predictions top {len(predictions)}: {predictions[:5]}... (cycle analysis)")
            return predictions
        except Exception as e:
            print(f"      ❌ Pattern Predictions error: {e}")
            return list(range(31, 31 + num_predictions))

    def get_ensemble_predictions(self, day_results, actual_results=None):
        """Phase 2: Kết hợp nhiều phương pháp với trọng số adaptive"""
        try:
            print(f"      🎯 ENSEMBLE PREDICTIONS (Phase 2 - Adaptive):")

            # 1. Các phương pháp cơ bản
            lstm_pred = self.get_lstm_predictions(day_results, 15)
            excluded_pred = list(self.get_excluded_numbers(day_results))

            # 2. Phương pháp bổ sung
            hot_numbers = self.get_hot_numbers(day_results, 10)
            cold_numbers = self.get_cold_numbers(day_results, 10)
            pattern_pred = self.get_pattern_predictions(day_results, 15)

            print(f"      📝 PHƯƠNG PHÁP DỰ ĐOÁN:")
            print(f"         🤖 LSTM: Dự đoán số có khả năng TRƯỢT cao (tần suất thấp trong ngày)")
            print(f"         🚫 EXCLUDED: Dự đoán số có khả năng TRƯỢT cao (gap analysis)")
            print(f"         🔥 HOT: Số nóng (xuất hiện nhiều gần đây) - có thể RA")
            print(f"         ❄️ COLD: Số lạnh (ít xuất hiện gần đây) - có thể TRƯỢT")
            print(f"         🔄 PATTERN: Dự đoán theo chu kỳ xuất hiện - có thể RA hoặc TRƯỢT")

            # 3. Tạo predictions dictionary để tracking
            predictions_dict = {
                'lstm': lstm_pred,
                'excluded': excluded_pred,
                'pattern': pattern_pred,
                'hot': hot_numbers,
                'cold': cold_numbers
            }

            # 4. Phase 2: Cập nhật performance nếu có actual results
            if actual_results is not None:
                self.update_method_performance(predictions_dict, actual_results)
                self.calculate_adaptive_weights()

            # 5. Phase 2: Tính confidence score
            confidence = self.calculate_confidence_score(predictions_dict)

            # 6. Phase 2: Sử dụng adaptive weights
            weights = self.method_weights
            print(f"      📊 Current weights: {[(k, f'{v:.3f}') for k, v in weights.items()]}")

            # 7. Tính điểm tổng hợp với adaptive weights
            final_scores = {}

            for method, pred_list in predictions_dict.items():
                weight = weights[method]
                for i, num in enumerate(pred_list[:10]):
                    score = (10 - i) * weight
                    final_scores[num] = final_scores.get(num, 0) + score

            # 8. Chọn top 6 số tốt nhất
            sorted_scores = sorted(final_scores.items(), key=lambda x: x[1], reverse=True)
            final_6 = [num for num, _ in sorted_scores[:6]]

            print(f"      🎯 Ensemble Final 6: {final_6}")
            print(f"      📊 Top ensemble scores (tổng hợp 6 phương pháp): {[(num, f'{score:.2f}') for num, score in sorted_scores[:6]]}")
            print(f"      💡 Giải thích: Score cao = số được nhiều phương pháp ưu tiên (bao gồm cả số trượt và số ra)")

            return final_6, confidence, predictions_dict
        except Exception as e:
            print(f"      ❌ Ensemble error: {e}")
            # Fallback to original weighted strategy
            fallback = self.get_weighted_strategy_fallback(day_results)
            return fallback, 0.5, {}

    def get_weighted_strategy_fallback(self, day_results):
        """Fallback strategy khi ensemble fail"""
        try:
            lstm_predictions = self.get_lstm_predictions(day_results, 15)
            excluded_numbers = list(self.get_excluded_numbers(day_results))

            # Tính trọng số cho từng số (logic cũ)
            weight_scores = {}

            for i, num in enumerate(lstm_predictions[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)

            for i, num in enumerate(excluded_numbers[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)

            sorted_by_weight = sorted(weight_scores.items(), key=lambda x: x[1], reverse=True)
            return [num for num, _ in sorted_by_weight[:6]]
        except Exception as e:
            print(f"      ❌ Fallback error: {e}")
            return list(range(1, 7))

    def is_balanced_combination(self, combo):
        """Kiểm tra combination có cân bằng không"""
        try:
            # Cân bằng chẵn/lẻ (2:2 hoặc 3:1)
            even_count = sum(1 for n in combo if n % 2 == 0)
            if even_count < 1 or even_count > 3:
                return False

            # Cân bằng thấp/cao (2:2 hoặc 3:1)
            low_count = sum(1 for n in combo if n <= 40)
            if low_count < 1 or low_count > 3:
                return False

            # Không có số liên tiếp quá nhiều
            sorted_combo = sorted(combo)
            consecutive = 0
            for i in range(1, len(sorted_combo)):
                if sorted_combo[i] - sorted_combo[i-1] == 1:
                    consecutive += 1
            if consecutive > 2:
                return False

            return True
        except Exception as e:
            print(f"      ❌ Balance check error: {e}")
            return True  # Default to True if error

    def generate_smart_combinations(self, final_6_numbers, num_combos=15):
        """Tạo combinations thông minh thay vì random"""
        try:
            if len(final_6_numbers) < 4:
                return []

            print(f"      🎲 SMART COMBINATIONS (Phase 1):")

            # 1. Tạo tất cả combinations có thể
            all_possible_combos = list(combinations(final_6_numbers, 4))

            # 2. Lọc combinations cân bằng
            balanced_combos = []
            for combo in all_possible_combos:
                if self.is_balanced_combination(combo):
                    balanced_combos.append(combo)

            print(f"      📊 Balanced combos: {len(balanced_combos)}/{len(all_possible_combos)}")

            # 3. Nếu có đủ balanced combos, ưu tiên chúng
            if len(balanced_combos) >= num_combos:
                selected_combos = random.sample(balanced_combos, num_combos)
                print(f"      ✅ Using {num_combos} balanced combinations")
            elif len(balanced_combos) > 0:
                # Dùng tất cả balanced + thêm random
                remaining = num_combos - len(balanced_combos)
                unbalanced_combos = [c for c in all_possible_combos if c not in balanced_combos]
                additional = random.sample(unbalanced_combos, min(remaining, len(unbalanced_combos)))
                selected_combos = balanced_combos + additional
                print(f"      ⚖️ Using {len(balanced_combos)} balanced + {len(additional)} random")
            else:
                # Fallback to random
                selected_combos = random.sample(all_possible_combos, min(num_combos, len(all_possible_combos)))
                print(f"      🎲 Fallback to {len(selected_combos)} random combinations")

            return selected_combos
        except Exception as e:
            print(f"      ❌ Smart combinations error: {e}")
            # Fallback to original random method
            all_possible_combos = list(combinations(final_6_numbers, 4))
            num_combos = min(num_combos, len(all_possible_combos))
            return random.sample(all_possible_combos, num_combos) if all_possible_combos else []

    def update_method_performance(self, predictions_dict, actual_results):
        """Phase 2: Cập nhật performance của từng phương pháp"""
        try:
            for method, pred_list in predictions_dict.items():
                if method in self.method_performance:
                    # Tính accuracy: số lượng số dự đoán đúng / tổng số dự đoán
                    hits = len(set(pred_list[:6]) & set(actual_results))
                    accuracy = hits / 6.0

                    # Thêm vào performance history
                    self.method_performance[method].append(accuracy)

                    # Chỉ giữ performance_window kết quả gần nhất
                    if len(self.method_performance[method]) > self.performance_window:
                        self.method_performance[method].pop(0)

                    print(f"      📊 {method.upper()} accuracy: {accuracy:.2f} ({hits}/6 hits)")
        except Exception as e:
            print(f"      ❌ Performance update error: {e}")

    def calculate_adaptive_weights(self):
        """Phase 2: Tính trọng số adaptive dựa trên performance"""
        try:
            new_weights = {}
            total_performance = 0

            for method in self.method_weights.keys():
                if self.method_performance[method]:
                    # Lấy performance trung bình của 10 kì gần nhất
                    recent_performance = self.method_performance[method][-10:]
                    avg_performance = sum(recent_performance) / len(recent_performance)

                    # Boost performance bằng cách thêm base score
                    boosted_performance = max(0.1, avg_performance + 0.1)  # Tối thiểu 10%
                    new_weights[method] = boosted_performance
                    total_performance += boosted_performance
                else:
                    # Sử dụng weight mặc định nếu chưa có data
                    new_weights[method] = self.method_weights[method]
                    total_performance += self.method_weights[method]

            # Normalize weights
            if total_performance > 0:
                for method in new_weights:
                    new_weights[method] = new_weights[method] / total_performance

                # Cập nhật weights
                old_weights = self.method_weights.copy()
                self.method_weights = new_weights

                print(f"      🎯 ADAPTIVE WEIGHTS UPDATE:")
                for method in self.method_weights:
                    old_w = old_weights[method]
                    new_w = self.method_weights[method]
                    change = ((new_w - old_w) / old_w) * 100 if old_w > 0 else 0
                    print(f"         {method}: {old_w:.3f} → {new_w:.3f} ({change:+.1f}%)")

            return self.method_weights
        except Exception as e:
            print(f"      ❌ Adaptive weights error: {e}")
            return self.method_weights

    def calculate_confidence_score(self, predictions_dict):
        """Phase 2: Tính confidence score cho prediction hiện tại"""
        try:
            # 1. Tính độ nhất quán giữa các methods
            all_predictions = []
            for pred_list in predictions_dict.values():
                all_predictions.extend(pred_list[:6])

            # Đếm số lần xuất hiện của mỗi số
            prediction_counts = Counter(all_predictions)

            # 2. Tính consensus score (số nào được nhiều method chọn)
            consensus_score = 0
            for count in prediction_counts.values():
                if count >= 2:  # Xuất hiện ở ít nhất 2 methods
                    consensus_score += count

            consensus_score = min(consensus_score / 20, 1.0)  # Normalize to 0-1

            # 3. Tính performance score dựa trên lịch sử
            performance_score = 0
            total_methods = 0
            for _, performances in self.method_performance.items():
                if performances:
                    recent_perf = performances[-5:]  # 5 kì gần nhất
                    avg_perf = sum(recent_perf) / len(recent_perf)
                    performance_score += avg_perf
                    total_methods += 1

            if total_methods > 0:
                performance_score = performance_score / total_methods
            else:
                performance_score = 0.5  # Default

            # 4. Tính confidence tổng hợp
            confidence = (consensus_score * 0.6 + performance_score * 0.4)

            # Lưu confidence score
            self.confidence_scores.append(confidence)
            if len(self.confidence_scores) > self.performance_window:
                self.confidence_scores.pop(0)

            print(f"      🎯 CONFIDENCE SCORE: {confidence:.3f} (consensus: {consensus_score:.3f}, performance: {performance_score:.3f})")

            return confidence
        except Exception as e:
            print(f"      ❌ Confidence calculation error: {e}")
            return 0.5  # Default confidence

    def get_optimal_ticket_count(self, confidence_score, base_tickets=10):
        """Phase 2: Tính số vé tối ưu dựa trên confidence"""
        try:
            if confidence_score > 0.8:
                multiplier = 1.5  # Tăng 50% khi confidence cao
                strategy = "HIGH CONFIDENCE"
            elif confidence_score > 0.6:
                multiplier = 1.2  # Tăng 20% khi confidence khá
                strategy = "MEDIUM CONFIDENCE"
            elif confidence_score < 0.3:
                multiplier = 0.7  # Giảm 30% khi confidence thấp
                strategy = "LOW CONFIDENCE"
            else:
                multiplier = 1.0  # Giữ nguyên
                strategy = "NORMAL CONFIDENCE"

            optimal_tickets = int(base_tickets * multiplier)
            optimal_tickets = max(3, min(optimal_tickets, 20))  # Giới hạn 3-20 vé

            print(f"      🎯 CONFIDENCE BETTING: {strategy} ({confidence_score:.3f}) → {optimal_tickets} vé (x{multiplier})")

            return optimal_tickets
        except Exception as e:
            print(f"      ❌ Optimal ticket count error: {e}")
            return base_tickets

    def _ensure_unique_7_numbers(self, initial_list, lstm_predictions, excluded_numbers):
        """Đảm bảo có đúng 7 số unique với logic fallback thông minh"""
        # Loại bỏ duplicate từ initial list
        unique_numbers = []
        seen = set()
        for num in initial_list:
            if num not in seen:
                unique_numbers.append(num)
                seen.add(num)

        # Nếu đã đủ 7 số unique
        if len(unique_numbers) >= 7:
            return unique_numbers[:7]

        # Cần thêm số - ưu tiên từ top 4-5 của các feature
        fallback_candidates = []

        # Thêm từ LSTM top 4-5
        if len(lstm_predictions) > 3:
            fallback_candidates.extend(lstm_predictions[3:5])

        # Thêm từ Excluded top 4-5
        if len(excluded_numbers) > 3:
            fallback_candidates.extend(list(excluded_numbers)[3:5])

        # Thêm các số chưa có từ fallback candidates
        for num in fallback_candidates:
            if num not in seen and len(unique_numbers) < 7:
                unique_numbers.append(num)
                seen.add(num)

        # Nếu vẫn chưa đủ, thêm số từ 1-80
        for num in range(1, 81):
            if num not in seen and len(unique_numbers) < 7:
                unique_numbers.append(num)
                seen.add(num)

        return unique_numbers[:7]

    def predict_period(self, day_draws, period_index):
        """Dự đoán cho một kì cụ thể"""
        if period_index >= len(day_draws):
            return None

        # Lấy dữ liệu training (tất cả kì trước đó)
        training_data = [day_draws[i]['results'] for i in range(period_index)]
        if len(training_data) < 10:
            return None

        # Kết quả thực tế của kì cần dự đoán
        actual_results = day_draws[period_index]['results']
        actual_missing = [i for i in range(1, 81) if i not in actual_results]

        # Debug: Kiểm tra training data
        print(f"      🔍 Training data: {len(training_data)} kì, sample: {training_data[-1] if training_data else 'None'}")

        # Tạo day_draws format cho các hàm prediction
        day_draws_format = [{'results': results} for results in training_data]

        # PHASE 2: ADAPTIVE ENSEMBLE PREDICTIONS - Kết hợp nhiều phương pháp với learning
        try:
            # Lấy actual results để update performance (nếu có)
            actual_missing = [i for i in range(1, 81) if i not in actual_results]

            final_6_strategy, confidence_score, _ = self.get_ensemble_predictions(
                day_draws_format, actual_missing
            )
        except Exception as e:
            print(f"      ❌ Ensemble failed: {e}, using fallback")
            # Fallback to original weighted strategy
            lstm_predictions = self.get_lstm_predictions(day_draws_format, 15)
            excluded_numbers = list(self.get_excluded_numbers(day_draws_format))

            # Tính trọng số cho từng số (logic cũ)
            weight_scores = {}
            for i, num in enumerate(lstm_predictions[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)
            for i, num in enumerate(list(excluded_numbers)[:10]):
                weight_scores[num] = weight_scores.get(num, 0) + (10 - i)

            sorted_by_weight = sorted(weight_scores.items(), key=lambda x: x[1], reverse=True)
            final_6_strategy = [num for num, _ in sorted_by_weight[:6]]
            confidence_score = 0.5  # Default confidence
            print(f"      🎯 Fallback weights: {[(num, weight) for num, weight in sorted_by_weight[:6]]}")

        # PHASE 2: CONFIDENCE-BASED COMBINATIONS - Tạo combinations dựa trên confidence
        if len(final_6_strategy) >= 4:
            # Số combinations dựa trên confidence
            base_combos = 12
            optimal_combos = self.get_optimal_ticket_count(confidence_score, base_combos)
            selected_combos = self.generate_smart_combinations(final_6_strategy, optimal_combos)

            # Kiểm tra vé thắng (legacy - để tương thích với code cũ)
            winning_combos = 0
            for combo in selected_combos:
                missing_count = sum(1 for num in combo if num in actual_missing)
                if missing_count >= 4:  # Thắng nếu cả 4 số đều trượt
                    winning_combos += 1

            total_combos = len(selected_combos)
        else:
            winning_combos = 0
            total_combos = 0
            selected_combos = []
        
        return {
            'period': period_index + 1,
            'time': day_draws[period_index]['time'],
            'actual_results': actual_results,
            'final_6_strategy': final_6_strategy,
            'selected_combos': selected_combos,
            'winning_combos': winning_combos,
            'total_combos': total_combos
        }

    def test_date_range(self, start_date, end_date, min_periods=50):
        """Test từ ngày start_date đến end_date"""
        print(f"\n🎯 TESTING DATE RANGE: {start_date} → {end_date}")
        print(f"⚙️ Config: Minimum periods = {min_periods}")
        print("="*60)
        
        # Tạo danh sách ngày test
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        test_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            test_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)
        
        # Thống kê tổng hợp
        total_stats = {
            'winning_combos': 0,
            'total_combos': 0,
            'total_predictions': 0,
            'total_days': 0,
            'daily_stats': {},  # Lưu thống kê từng ngày
            'total_profit': 0,
            'total_cost': 0,
            'total_revenue': 0
        }
        
        for test_date in test_dates:
            print(f"\n📅 Testing {test_date}...")
            
            day_draws = self.get_day_draws(test_date)
            if len(day_draws) < min_periods + 1:
                print(f"   ⚠️ Không đủ dữ liệu (có {len(day_draws)}, cần {min_periods + 1})")
                continue
            
            # Test từ kì min_periods+1 đến cuối ngày với money management
            max_period = min(len(day_draws), 119)
            day_predictions = 0
            day_winning = 0
            day_total = 0

            # Money management cho ngày
            current_capital = self.daily_capital
            daily_profit = 0
            daily_cost = 0
            daily_revenue = 0
            consecutive_losses = 0
            consecutive_wins = 0
            total_bets = 0
            recent_big_win = False
            last_period_profit = 0

            # Tracking cho skip strategy
            self._period_results = {}  # Lưu kết quả từng kì để tính win rate

            # Tính win rate từ historical data (dùng 36% từ test trước)
            estimated_win_rate = 0.36

            for period_index in range(min_periods, max_period):
                # Cập nhật Martingale tracking
                self.update_martingale_tracking()

                # Tính win rate gần đây (10 kì gần nhất) - chỉ khi đã có ít nhất 5 kì dữ liệu
                recent_periods = max(5, min(period_index - min_periods, 15))
                if recent_periods >= 5 and hasattr(self, '_period_results') and len(self._period_results) >= 5:
                    recent_wins = sum(1 for i in range(max(0, period_index - recent_periods), period_index)
                                    if i in self._period_results and self._period_results[i] > 0)
                    recent_win_rate = recent_wins / recent_periods
                else:
                    recent_win_rate = estimated_win_rate  # Sử dụng estimated win rate khi chưa có đủ dữ liệu

                # Kiểm tra có nên skip không
                skip_result = self.should_skip_prediction(recent_win_rate, consecutive_losses)
                if skip_result:
                    print(f"   ⏸️ KÌ {period_index + 1} - SKIP (dự đoán khó)")
                    continue

                # Kiểm tra điều kiện dừng
                if daily_profit >= self.target_profit:
                    print(f"   🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                    break

                if current_capital <= self.daily_capital * self.stop_loss_ratio:
                    print(f"   🛑 STOP LOSS! Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%) - DỪNG CHƠI")
                    break

                result = self.predict_period(day_draws, period_index)
                if result:
                    # Detect big win (lợi nhuận kì trước > 100k)
                    recent_big_win = last_period_profit > 100_000

                    # Tính giá vé động với logic mới
                    base_ticket_price = self.calculate_dynamic_ticket_price(consecutive_losses, consecutive_wins, daily_profit, recent_big_win)

                    # Tính số vé cần mua
                    base_tickets = self.calculate_bet_size(current_capital, consecutive_losses, estimated_win_rate, base_ticket_price, consecutive_wins, daily_profit)

                    # Sử dụng base values - không có strategy adjustments
                    tickets_to_buy = int(base_tickets)
                    ticket_price = base_ticket_price

                    # CHIẾN THUẬT MỚI: Nếu không đủ vé, tăng giá vé thay vì tăng số vé
                    min_tickets_required = 5  # Tối thiểu cần 5 vé
                    if tickets_to_buy < min_tickets_required and tickets_to_buy > 0:
                        # Tính lại giá vé để có đủ min_tickets_required
                        available_budget = current_capital * 0.1  # Dùng tối đa 10% vốn
                        new_ticket_price = available_budget / min_tickets_required

                        if new_ticket_price >= self.min_ticket_price:
                            ticket_price = min(new_ticket_price, self.max_ticket_price * 3)  # Cho phép vượt max
                            tickets_to_buy = min_tickets_required
                            print(f"      💰 Điều chỉnh: {tickets_to_buy} vé @ {ticket_price:,.0f} VNĐ/vé (tăng giá thay vì giảm vé)")

                    if tickets_to_buy == 0:
                        print(f"   💸 Không đủ vốn để mua vé - DỪNG CHƠI")
                        break

                    # Tính số vé thắng - CHỈ TÍNH TRÊN CÁC VÉ THỰC TẾ MUA
                    tickets_to_display = int(min(tickets_to_buy, len(result['selected_combos'])))
                    actual_tickets_bought = result['selected_combos'][:tickets_to_display]

                    winning_tickets = 0
                    winning_details = []
                    actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]

                    for i, combo in enumerate(actual_tickets_bought, 1):
                        missing_count = sum(1 for num in combo if num in actual_missing)
                        is_winning = missing_count >= 4
                        if is_winning:
                            winning_tickets += 1
                        winning_details.append({
                            'ticket_num': i,
                            'combo': combo,
                            'missing_count': missing_count,
                            'is_winning': is_winning
                        })

                    # Tính lợi nhuận dựa trên số vé thực tế mua
                    period_profit, period_cost, period_revenue = self.calculate_profit(tickets_to_display, winning_tickets, ticket_price)

                    # Cập nhật vốn và thống kê
                    current_capital += period_profit
                    daily_profit += period_profit
                    daily_cost += period_cost
                    daily_revenue += period_revenue

                    total_bets += 1

                    # Cập nhật consecutive wins/losses và lưu profit kì này
                    last_period_profit = period_profit

                    # Tính tỷ lệ trượt cho kỳ này
                    actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]
                    predicted_numbers = result['final_6_strategy']

                    # Tính số dự đoán trượt đúng (số dự đoán có trong actual_missing)
                    correct_misses = sum(1 for num in predicted_numbers if num in actual_missing)
                    miss_rate = correct_misses / len(predicted_numbers) if predicted_numbers else 0

                    # Lưu kết quả kì này để tính win rate và miss rate
                    self._period_results[period_index] = period_profit

                    # Cập nhật recent_results với miss_rate
                    period_result = {
                        'period': period_index + 1,
                        'profit': period_profit,
                        'miss_rate': miss_rate,
                        'predicted_numbers': predicted_numbers,
                        'actual_missing': actual_missing
                    }

                    # Thêm vào recent_results và giữ tối đa 10 kỳ gần nhất
                    if not hasattr(self, 'recent_results'):
                        self.recent_results = []
                    self.recent_results.append(period_result)
                    if len(self.recent_results) > 10:
                        self.recent_results.pop(0)

                    if period_profit > 0:
                        consecutive_losses = 0
                        consecutive_wins += 1
                    else:
                        consecutive_losses += 1
                        consecutive_wins = 0

                    day_predictions += 1
                    day_winning += winning_tickets  # Sử dụng số vé thắng thực tế
                    day_total += tickets_to_display  # Sử dụng số vé thực tế mua
                    total_stats['total_predictions'] += 1
                    total_stats['winning_combos'] += winning_tickets  # Sử dụng số vé thắng thực tế
                    total_stats['total_combos'] += tickets_to_display  # Sử dụng số vé thực tế mua

                    # Cập nhật thống kê profit
                    total_stats['total_profit'] += period_profit
                    total_stats['total_cost'] += period_cost
                    total_stats['total_revenue'] += period_revenue

                    # Tính target progress
                    target_progress = (daily_profit / self.target_profit) * 100

                    # Log chi tiết với money management
                    self._print_period_details_with_money_v2(result, tickets_to_display, winning_tickets,
                                                        period_profit, current_capital, consecutive_losses,
                                                        daily_profit, target_progress, ticket_price, consecutive_wins,
                                                        winning_details, period_cost, period_revenue)
            
            if day_predictions > 0:
                total_stats['total_days'] += 1
                win_rate = (day_winning / day_total) * 100 if day_total > 0 else 0
                avg_winning_per_period = day_winning / day_predictions if day_predictions > 0 else 0

                # Lưu thống kê ngày
                total_stats['daily_stats'][test_date] = {
                    'predictions': day_predictions,
                    'total_combos': day_total,
                    'winning_combos': day_winning,
                    'win_rate': win_rate,
                    'daily_profit': daily_profit
                }

                print(f"   📊 Ngày {test_date}: {day_predictions} kì, {day_winning}/{day_total} vé thắng ({win_rate:.1f}%)")
                print(f"   📈 Trung bình: {avg_winning_per_period:.1f} vé thắng/kì")

                # Thêm daily summary
                print(f"\n   📊 THỐNG KÊ NGÀY {test_date}:")
                print(f"      • Tổng kì dự đoán: {day_predictions}")
                print(f"      • Tổng vé đánh: {day_total}")
                print(f"      • Tổng vé thắng: {day_winning}")
                print(f"      • Tỷ lệ thắng: {win_rate:.1f}%")
                print(f"      • Trung bình vé thắng/kì: {avg_winning_per_period:.1f}")
                print("   " + "="*50)
        
        # Hiển thị thống kê tổng hợp
        self._display_final_results(total_stats, start_date, end_date)

    def _print_period_details_with_money_v2(self, result, tickets_bought, winning_tickets,
                                        period_profit, current_capital, consecutive_losses,
                                        cumulative_profit, target_progress, ticket_price, consecutive_wins,
                                        winning_details=None, period_cost=0, period_revenue=0):
        """In chi tiết kết quả của một kì với money management và lợi nhuận tích lũy"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")

        # In Final 6 Strategy (rút gọn, weighted order)
        print(f"      🎯 Final 6: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])} (weighted order)")

        # Hiển thị các vé 4 số đã mua (chỉ hiển thị 5 vé đầu để không quá dài)
        if winning_details:
            print(f"      🎫 Vé đã mua ({len(winning_details)} vé):")
            for i, detail in enumerate(winning_details[:5], 1):  # Chỉ hiển thị 5 vé đầu
                status_icon = "✅" if detail['is_winning'] else "❌"
                sorted_combo = sorted(detail['combo'])
                combo_str = ' '.join([f'{num:2d}' for num in sorted_combo])
                print(f"         {i}: {combo_str} → {detail['missing_count']}/4 {status_icon}")
            if len(winning_details) > 5:
                print(f"         ... và {len(winning_details) - 5} vé khác")

        # In money management với dynamic pricing
        profit_icon = "🟢" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"

        # Hiển thị thông tin martingale/aggressive
        if consecutive_losses >= 5:
            print(f"      🔥 MARTINGALE 100%: Thua {consecutive_losses} kì → Tăng SỐ VÉ x2 + GIÁ VÉ x2 (3 kì)")
        elif consecutive_losses >= 3:
            print(f"      📈 MARTINGALE 50%: Thua {consecutive_losses} kì → Tăng SỐ VÉ x1.5 + GIÁ VÉ x1.5 (2 kì)")
        elif consecutive_wins >= 6:
            print(f"      🚀 AGGRESSIVE 300%: Thắng {consecutive_wins} kì → Tăng SỐ VÉ x3 + GIÁ VÉ x3")
        elif consecutive_wins >= 3:
            print(f"      🔥 AGGRESSIVE 200%: Thắng {consecutive_wins} kì → Tăng SỐ VÉ x2 + GIÁ VÉ x2")
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            print(f"      💎 PROFIT-BASED: Thắng {consecutive_wins} kì, lời {cumulative_profit:,} VNĐ → Dùng 50% lời để tăng cược")

        if period_cost > 0 and period_revenue > 0:
            print(f"      💰 Chi phí: {tickets_bought} vé × {ticket_price:,} = {period_cost:,} VNĐ")
            print(f"      💰 Thu nhập: {winning_tickets} vé × {ticket_price:,} × {self.win_multiplier} = {period_revenue:,} VNĐ")
            print(f"      💰 Lợi nhuận: {profit_icon} {period_profit:+,} VNĐ")
        else:
            print(f"      💰 Mua {tickets_bought} vé @ {ticket_price:,} VNĐ/vé → Thắng {winning_tickets} vé → {profit_icon} {period_profit:+,} VNĐ")
        print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")

        # Hiển thị thông tin giá vé động
        price_status = ""
        if ticket_price > self.base_ticket_price:
            price_increase = ((ticket_price - self.base_ticket_price) / self.base_ticket_price) * 100
            if price_increase >= 200:
                price_status = f"🔥 Tăng giá {price_increase:.0f}% (MARTINGALE 300%)"
            elif price_increase >= 100:
                price_status = f"📈 Tăng giá {price_increase:.0f}% (MARTINGALE 200%)"
            elif price_increase >= 40:
                price_status = f"📈 Tăng giá {price_increase:.0f}% (thắng đậm - aggressive)"
            elif price_increase >= 20:
                if consecutive_wins >= 2:
                    price_status = f"📈 Tăng giá {price_increase:.0f}% (đang lãi - aggressive)"
                else:
                    price_status = f"� Tăng giá {price_increase:.0f}% (thua 3 kì liên tiếp)"
            else:
                price_status = f"📈 Tăng giá {price_increase:.1f}%"
        else:
            price_status = "📊 Giá cơ bản"
        print(f"      🎫 {price_status}")

        # Thêm logs lợi nhuận tích lũy
        cumulative_icon = "🟢" if cumulative_profit > 0 else "🔴" if cumulative_profit < 0 else "🟡"
        print(f"      📈 Lợi nhuận tích lũy: {cumulative_icon} {cumulative_profit:+,} VNĐ")
        print(f"      🎯 Tiến độ mục tiêu: {target_progress:.1f}% ({cumulative_profit:,}/{self.target_profit:,} VNĐ)")

        # Hiển thị còn thiếu bao nhiêu để đạt mục tiêu
        remaining = self.target_profit - cumulative_profit
        if remaining > 0:
            print(f"      📊 Còn thiếu: {remaining:,} VNĐ để đạt mục tiêu")
        else:
            print(f"      🎉 ĐÃ VƯỢT MỤC TIÊU: +{abs(remaining):,} VNĐ")

        if consecutive_losses > 0:
            print(f"      ⚠️ Thua liên tiếp: {consecutive_losses} lần")

        # Prediction stats (rút gọn)
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"      📊 Prediction: {result['winning_combos']}/{result['total_combos']} ({win_rate:.1f}%)")

        # Hiển thị miss rate nếu có
        if hasattr(self, 'recent_results') and self.recent_results:
            latest_result = self.recent_results[-1]
            if 'miss_rate' in latest_result:
                correct_misses = int(latest_result['miss_rate'] * len(latest_result['predicted_numbers']))
                total_predicted = len(latest_result['predicted_numbers'])
                print(f"      🎯 Miss Rate: {latest_result['miss_rate']:.1%} ({correct_misses}/{total_predicted} số trượt đúng)")

    def _print_period_details_with_money(self, result, tickets_bought, winning_tickets,
                                        period_profit, current_capital, consecutive_losses):
        """In chi tiết kết quả của một kì với money management"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")

        # In Final 6 Strategy (rút gọn)
        print(f"      🎯 Final 6: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])}")

        # In money management
        profit_status = "�" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"
        print(f"      💰 Mua {tickets_bought} vé → Thắng {winning_tickets} vé → {profit_status} {period_profit:+,} VNĐ")
        print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")

        if consecutive_losses > 0:
            print(f"      ⚠️ Thua liên tiếp: {consecutive_losses} lần")

        # Prediction stats (rút gọn)
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"      📊 Prediction: {result['winning_combos']}/{result['total_combos']} ({win_rate:.1f}%)")

    def _print_period_details(self, result):
        """In chi tiết kết quả của một kì (legacy method)"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")
        print(f"      📊 Kết quả thực tế: {result['actual_results']}")

        # In Final 6 Strategy
        print(f"      🎯 Final 6 Strategy:")
        print(f"         Numbers: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])}")
        print(f"         Source:  Weighted combination from LSTM + Excluded + Pattern + Hot + Cold")
        print(f"         Legend:  Top 6 numbers by weight score")

        # In thống kê bộ 4 số
        print(f"      🎲 Random 4-number combos:")
        print(f"         Vé thắng: {result['winning_combos']}/{result['total_combos']} vé")
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"         Tỷ lệ thắng: {win_rate:.1f}%")

    def _display_final_results(self, total_stats, start_date, end_date):
        """Hiển thị kết quả tổng hợp"""
        print(f"\n🎯 THỐNG KÊ TỔNG HỢP ({start_date} → {end_date})")
        print("="*60)
        print(f"Tổng: {total_stats['total_days']} ngày, {total_stats['total_predictions']} predictions")

        # Hiển thị thống kê từng ngày
        if total_stats['daily_stats']:
            print(f"\n📊 THỐNG KÊ TỪNG NGÀY:")
            print("   Ngày        | Kì  | Vé thắng/Tổng vé | Tỷ lệ % | Lợi nhuận")
            print("   " + "-"*62)

            for date, stats in total_stats['daily_stats'].items():
                profit_display = f"{stats['daily_profit']:+,.0f}" if stats['daily_profit'] != 0 else "0"
                print(f"   {date} | {stats['predictions']:2d}  | "
                      f"{stats['winning_combos']:3d}/{stats['total_combos']:3d}        | "
                      f"{stats['win_rate']:5.1f}% | {profit_display:>10}")

        print(f"\n💰 MONEY MANAGEMENT RESULTS:")
        if total_stats['total_cost'] > 0:
            total_roi = (total_stats['total_profit'] / total_stats['total_cost']) * 100
            print(f"   Tổng chi phí: {total_stats['total_cost']:,} VNĐ")
            print(f"   Tổng doanh thu: {total_stats['total_revenue']:,} VNĐ")
            print(f"   Tổng lợi nhuận: {total_stats['total_profit']:+,} VNĐ")
            print(f"   ROI: {total_roi:+.1f}%")
            print(f"   Lợi nhuận/ngày: {total_stats['total_profit']/total_stats['total_days']:+,.0f} VNĐ")

            # Đánh giá mục tiêu
            target_achievement = (total_stats['total_profit']/total_stats['total_days']) / self.target_profit * 100
            if target_achievement >= 100:
                print(f"   🎯 ĐẠT MỤC TIÊU: {target_achievement:.1f}% mục tiêu ({self.target_profit:,} VNĐ/ngày)")
            else:
                print(f"   ⚠️ CHƯA ĐẠT MỤC TIÊU: {target_achievement:.1f}% mục tiêu ({self.target_profit:,} VNĐ/ngày)")

        print(f"\n🎲 Trading Performance Overall Stats:")
        if total_stats['total_combos'] > 0:
            win_rate = (total_stats['winning_combos'] / total_stats['total_combos']) * 100
            print(f"   Tổng vé thắng: {total_stats['winning_combos']}")
            print(f"   Tổng vé đã mua: {total_stats['total_combos']}")
            print(f"   Tỷ lệ thắng vé: {win_rate:.1f}%")
            print(f"   Trung bình vé thắng/kì: {total_stats['winning_combos']/total_stats['total_predictions']:.1f}")
            print(f"   Trung bình vé mua/kì: {total_stats['total_combos']/total_stats['total_predictions']:.1f}")

            # Thống kê performance
            if total_stats['daily_stats']:
                daily_rates = [stats['win_rate'] for stats in total_stats['daily_stats'].values()]
                avg_daily_rate = sum(daily_rates) / len(daily_rates)
                max_daily_rate = max(daily_rates)
                min_daily_rate = min(daily_rates)

                print(f"\n📈 Performance Analysis:")
                print(f"   Tỷ lệ thắng trung bình/ngày: {avg_daily_rate:.1f}%")
                print(f"   Tỷ lệ thắng cao nhất: {max_daily_rate:.1f}%")
                print(f"   Tỷ lệ thắng thấp nhất: {min_daily_rate:.1f}%")
        else:
            print(f"   No data available")

        print("="*60)

def test_range(start_date, end_date, min_periods=50):
    """Function để test từ command line"""
    predictor = FinalKenoPredictor()
    return predictor.test_date_range(start_date, end_date, min_periods)

if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3 or len(sys.argv) > 4:
        print("Usage: python final_keno_predictor.py START_DATE END_DATE [MIN_PERIODS]")
        print("Example: python final_keno_predictor.py 2025-04-01 2025-04-30")
        print("Example: python final_keno_predictor.py 2025-04-01 2025-04-30 30")
        sys.exit(1)

    start_date = sys.argv[1]
    end_date = sys.argv[2]
    min_periods = int(sys.argv[3]) if len(sys.argv) == 4 else 50

    test_range(start_date, end_date, min_periods)
